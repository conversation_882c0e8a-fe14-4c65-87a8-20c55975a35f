using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using HappyWechat.Domain.Entities;
using HappyWechat.Application.DTOs.Wx;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.DTOs.SystemConfig;
using HappyWechat.Application.Constants;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.MessageQueue.Simplified;
using HappyWechat.Infrastructure.Services;
using HappyWechat.Infrastructure.MediaProcessing;
using HappyWechat.Infrastructure.Caching;
using HappyWechat.Infrastructure.MessageProcessing.Constants;
using HappyWechat.Infrastructure.Identity.Repositories;

namespace HappyWechat.Infrastructure.MessageProcessing;

/// <summary>
/// EYun回调处理器 - 完整实现消息流水线
/// 集成 WxManagerIdCacheService, EnhancedSensitiveWordService, AdvancedMessageSplitter, UnifiedMediaProcessor
/// </summary>
public class EYunCallbackProcessor : IEYunCallbackProcessor
{
    private readonly ILogger<EYunCallbackProcessor> _logger;
    private readonly IWxManagerIdCacheService _wxManagerIdService;
    private readonly IUnifiedIdManager _unifiedIdManager;
    private readonly IEnhancedSensitiveWordService _sensitiveWordService;
    private readonly IUnifiedMediaProcessor _mediaProcessor;
    private readonly IAiConfigHotReloadService _aiConfigService;
    private readonly ApplicationDbContext _dbContext;
    
    // 🚀 流式架构：智能消息路由器 - 毫秒级响应，永不阻塞
    private readonly HappyWechat.Infrastructure.MessageQueue.Streaming.IIntelligentMessageRouter _messageRouter;

    // 支持的消息类型 - 来自用户需求的10种类型
    private static readonly HashSet<string> SupportedMessageTypes = new()
    {
        MessageTypeConstants.PRIVATE_TEXT,      // 60001
        MessageTypeConstants.PRIVATE_IMAGE,     // 60002
        MessageTypeConstants.PRIVATE_VOICE,     // 60004
        MessageTypeConstants.PRIVATE_FILE,      // 60009
        MessageTypeConstants.GROUP_TEXT,        // 80001
        MessageTypeConstants.GROUP_IMAGE,       // 80002
        MessageTypeConstants.GROUP_VOICE,       // 80004
        MessageTypeConstants.GROUP_FILE,        // 80009
        MessageTypeConstants.OFFLINE_NOTIFICATION, // 30000
        "30001" // 系统消息（朋友请求等）
    };

    // 🚀 新架构：已彻底移除废弃的消息类型60008/80008

    public EYunCallbackProcessor(
        ILogger<EYunCallbackProcessor> logger,
        IWxManagerIdCacheService wxManagerIdService,
        IUnifiedIdManager unifiedIdManager,
        IEnhancedSensitiveWordService sensitiveWordService,
        IUnifiedMediaProcessor mediaProcessor,
        IAiConfigHotReloadService aiConfigService,
        ApplicationDbContext dbContext,
        HappyWechat.Infrastructure.MessageQueue.Streaming.IIntelligentMessageRouter messageRouter)
    {
        _logger = logger;
        _wxManagerIdService = wxManagerIdService;
        _unifiedIdManager = unifiedIdManager;
        _sensitiveWordService = sensitiveWordService;
        _mediaProcessor = mediaProcessor;
        _aiConfigService = aiConfigService;
        _dbContext = dbContext;
        _messageRouter = messageRouter;
    }

    public async Task<bool> ProcessCallbackAsync(object callbackData)
    {
        var processingId = Guid.NewGuid().ToString("N")[..8];
        
        try
        {
            // 1. 解析回调数据为DTO
            var callbackDto = await ParseCallbackDataAsync(callbackData, processingId);
            
            // 🚀 根据消息类型输出不同的日志格式
            LogCallbackDataReceived(callbackDto, processingId);
            if (callbackDto == null)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 回调数据解析失败，跳过处理", processingId);
                return false;
            }

            // 2. WcId到WxManagerId映射 - 使用统一ID管理器
            var wxManagerId = await _unifiedIdManager.ResolveWxManagerIdAsync(callbackDto.WcId);
            if (!wxManagerId.HasValue)
            {
                // 尝试确保映射存在
                var mappingEstablished = await _unifiedIdManager.EnsureMappingAsync(callbackDto.WcId!);
                if (!mappingEstablished)
                {
                    _logger.LogWarning("[{ProcessingId}] ⚠️ 无效的WcId: {WcId}，跳过处理", processingId, callbackDto.WcId);
                    return false;
                }
                
                // 重新获取映射
                wxManagerId = await _unifiedIdManager.ResolveWxManagerIdAsync(callbackDto.WcId);
                if (!wxManagerId.HasValue)
                {
                    _logger.LogError("[{ProcessingId}] ❌ 映射建立失败 - WcId: {WcId}", processingId, callbackDto.WcId);
                    return false;
                }
            }

            callbackDto.WxManagerId = wxManagerId.Value;

            // 3. 消息类型验证
            if (!IsSupportedMessageType(callbackDto.Type))
            {
                _logger.LogDebug("[{ProcessingId}] 🚫 不支持的消息类型: {MessageType}，跳过处理", processingId, callbackDto.Type);
                return true; // 不支持的消息类型不算错误
            }

            // 4. 实体存在性验证 - 按用户要求检查WxContact和WxGroup表
            var entityExists = await ValidateEntityExistenceAsync(callbackDto, processingId);
            if (!entityExists)
            {
                // 实体不存在，跳过处理
                return true; // 不存在的实体不算错误，只是跳过
            }

            // 5. 🚀 流式架构：路由到智能消息路由器 - 毫秒级响应，永不阻塞
            var success = await RouteToStreamingArchitectureAsync(callbackDto, processingId);
            
            if (success)
            {
                _logger.LogInformation("[{ProcessingId}] ✅ 回调数据处理完成 - MessageType: {MessageType}, WxManagerId: {WxManagerId}", 
                    processingId, callbackDto.Type, wxManagerId.Value);
            }
            
            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 回调数据处理异常", processingId);
            return false;
        }
    }

    public async Task<bool> ProcessMessageAsync(WxMessage message)
    {
        var processingId = Guid.NewGuid().ToString("N")[..8];
        
        try
        {
            _logger.LogInformation("[{ProcessingId}] 📨 处理微信消息 - MessageId: {MessageId}, Type: {Type}", 
                processingId, message.Id, message.Type);

            // 转换为回调DTO格式并处理
            var callbackDto = ConvertMessageToDto(message);
            return await ProcessCallbackAsync(callbackDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 微信消息处理失败 - MessageId: {MessageId}", 
                processingId, message.Id);
            return false;
        }
    }

    public async Task<bool> ProcessFriendRequestAsync(object friendRequest)
    {
        var processingId = Guid.NewGuid().ToString("N")[..8];
        
        try
        {
            _logger.LogInformation("[{ProcessingId}] 👥 处理好友请求", processingId);

            // 创建好友请求DTO
            var friendRequestDto = new WxCallbackDto
            {
                Type = "30001", // 系统消息 - 好友请求
                Content = Newtonsoft.Json.JsonConvert.SerializeObject(friendRequest),
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };

            return await ProcessCallbackAsync(friendRequestDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 好友请求处理失败", processingId);
            return false;
        }
    }

    public async Task<bool> ProcessGroupInviteAsync(object groupInvite)
    {
        var processingId = Guid.NewGuid().ToString("N")[..8];
        
        try
        {
            _logger.LogInformation("[{ProcessingId}] 👥 处理群邀请", processingId);

            // 创建群邀请DTO
            var groupInviteDto = new WxCallbackDto
            {
                Type = "30001", // 系统消息 - 群邀请
                Content = Newtonsoft.Json.JsonConvert.SerializeObject(groupInvite),
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };

            return await ProcessCallbackAsync(groupInviteDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 群邀请处理失败", processingId);
            return false;
        }
    }

    /// <summary>
    /// 解析回调数据为DTO
    /// </summary>
    private async Task<WxCallbackDto?> ParseCallbackDataAsync(object callbackData, string processingId)
    {
        try
        {
            string jsonString;
            
            if (callbackData is string str)
            {
                jsonString = str;
            }
            else
            {
                jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(callbackData);
            }

            // 先解析为EYun原生格式
            var eyunDto = Newtonsoft.Json.JsonConvert.DeserializeObject<WxCallbackMessageDto>(jsonString);
            if (eyunDto == null)
            {
                _logger.LogError("[{ProcessingId}] ❌ EYun数据解析失败", processingId);
                return null;
            }

            // 转换为内部DTO
            var dto = new WxCallbackDto
            {
                WcId = eyunDto.WcId,
                Type = eyunDto.MessageType,
                FromUser = eyunDto.Data?.FromUser,
                ToUser = eyunDto.Data?.ToUser,
                Content = eyunDto.Data?.Content,
                Timestamp = eyunDto.Data?.Timestamp ?? 0,
                WxManagerId = null, // 后续通过查找设置

                // 🔧 修复群组信息字段映射
                GroupName = eyunDto.Data?.FromGroup,
                FromGroup = eyunDto.Data?.FromGroup,  // 修复：正确赋值FromGroup字段

                // 🔧 修复：添加缺失的消息ID和@列表字段
                MsgId = eyunDto.Data?.MsgId ?? 0,
                NewMsgId = eyunDto.Data?.NewMsgId ?? 0,
                Atlist = eyunDto.Data?.Atlist ?? new List<string>()
            };

            // 处理特殊消息类型的字段
            if (eyunDto.Data != null)
            {
                // 对于图片、语音、文件等类型，从content中提取URL信息
                if (eyunDto.MessageType == MessageTypeConstants.PRIVATE_IMAGE || 
                    eyunDto.MessageType == MessageTypeConstants.GROUP_IMAGE)
                {
                    dto.ImageUrl = ExtractUrlFromXml(eyunDto.Data.Content);
                }
                else if (eyunDto.MessageType == MessageTypeConstants.PRIVATE_VOICE || 
                         eyunDto.MessageType == MessageTypeConstants.GROUP_VOICE)
                {
                    dto.VoiceUrl = ExtractUrlFromXml(eyunDto.Data.Content);
                }
                else if (eyunDto.MessageType == MessageTypeConstants.PRIVATE_FILE || 
                         eyunDto.MessageType == MessageTypeConstants.GROUP_FILE)
                {
                    dto.FileName = ExtractFileNameFromXml(eyunDto.Data.Content);
                }
            }

            _logger.LogDebug("[{ProcessingId}] 📋 回调数据解析成功 - Type: {Type}, WcId: {WcId}, FromUser: {FromUser}", 
                processingId, dto.Type, dto.WcId, dto.FromUser);

            return dto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 回调数据解析失败 - Data: {Data}", 
                processingId, callbackData?.ToString()?[..Math.Min(200, callbackData?.ToString()?.Length ?? 0)]);
            return null;
        }
    }

    /// <summary>
    /// 从XML内容中提取URL
    /// </summary>
    private string? ExtractUrlFromXml(string? content)
    {
        if (string.IsNullOrEmpty(content)) return null;
        
        try
        {
            // 简单的XML解析，提取URL相关信息
            // 根据实际EYun数据格式调整
            var startIndex = content.IndexOf("url=\"");
            if (startIndex >= 0)
            {
                startIndex += 5; // "url=\"".Length
                var endIndex = content.IndexOf("\"", startIndex);
                if (endIndex > startIndex)
                {
                    return content.Substring(startIndex, endIndex - startIndex);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "从XML内容中提取URL失败: {Content}", content);
        }
        
        return null;
    }

    /// <summary>
    /// 从XML内容中提取文件名
    /// </summary>
    private string? ExtractFileNameFromXml(string? content)
    {
        if (string.IsNullOrEmpty(content)) return null;
        
        try
        {
            // 简单的XML解析，提取文件名相关信息
            var startIndex = content.IndexOf("filename=\"");
            if (startIndex >= 0)
            {
                startIndex += 10; // "filename=\"".Length
                var endIndex = content.IndexOf("\"", startIndex);
                if (endIndex > startIndex)
                {
                    return content.Substring(startIndex, endIndex - startIndex);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "从XML内容中提取文件名失败: {Content}", content);
        }
        
        return null;
    }

    /// <summary>
    /// 获取WxManagerId
    /// </summary>
    private async Task<Guid> GetWxManagerIdAsync(string? wcId, string processingId)
    {
        if (string.IsNullOrEmpty(wcId))
        {
            _logger.LogWarning("[{ProcessingId}] ⚠️ WcId为空", processingId);
            return Guid.Empty;
        }

        try
        {
            var wxManagerId = await _wxManagerIdService.GetWxManagerIdAsync(wcId);
            
            if (wxManagerId == null || wxManagerId == Guid.Empty)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 无法获取WxManagerId - WcId: {WcId}", processingId, wcId);
                return Guid.Empty;
            }
            else
            {
                _logger.LogDebug("[{ProcessingId}] 🔗 WcId映射成功 - WcId: {WcId} -> WxManagerId: {WxManagerId}", 
                    processingId, wcId, wxManagerId);
            }

            return wxManagerId.Value;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ WxManagerId获取异常 - WcId: {WcId}", processingId, wcId);
            return Guid.Empty;
        }
    }

    /// <summary>
    /// 检查是否为支持的消息类型
    /// </summary>
    private bool IsSupportedMessageType(string? messageType)
    {
        if (string.IsNullOrEmpty(messageType))
            return false;

        // 检查是否为正常支持的类型
        if (SupportedMessageTypes.Contains(messageType))
            return true;

        // 🚀 新架构：已彻底移除废弃的消息类型60008/80008的处理逻辑
        // 明确忽略60008和80008消息类型
        if (messageType == "60008" || messageType == "80008")
        {
            _logger.LogWarning("⚠️ 忽略废弃的消息类型 - MessageType: {MessageType}, 请使用60009/80009替代", messageType);
            return false;
        }

        return false;
    }

    /// <summary>
    /// 验证实体存在性 - 根据消息类型进行正确的验证
    /// 8开头：群聊消息，验证群组存在性
    /// 6开头：私聊消息，验证联系人存在性
    /// 其他：系统消息，跳过验证
    /// </summary>
    private async Task<bool> ValidateEntityExistenceAsync(WxCallbackDto callbackDto, string processingId)
    {
        try
        {
            var messageType = callbackDto.Type;
            _logger.LogDebug("[{ProcessingId}] 🔍 开始实体存在性验证 - MessageType: {MessageType}", processingId, messageType);

            if (!callbackDto.WxManagerId.HasValue)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ WxManagerId为空，跳过实体验证", processingId);
                return false;
            }

            var wxManagerId = callbackDto.WxManagerId.Value;

            // 根据消息类型决定验证逻辑
            if (messageType?.StartsWith("8") == true)
            {
                // 8开头：群聊消息，验证群组存在性
                return await ValidateGroupExistenceAsync(callbackDto, wxManagerId, processingId);
            }
            else if (messageType?.StartsWith("6") == true)
            {
                // 6开头：私聊消息，验证联系人存在性
                return await ValidateContactExistenceAsync(callbackDto, wxManagerId, processingId);
            }
            else
            {
                // 其他消息类型（30000, 30001, 37等系统消息）：跳过实体验证
                _logger.LogDebug("[{ProcessingId}] ⏭️ 3字头系统消息，跳过实体验证直接处理 - MessageType: {MessageType}",
                    processingId, messageType);
                return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 实体存在性验证异常", processingId);
            return false;
        }
    }

    /// <summary>
    /// 验证群组存在性
    /// </summary>
    private async Task<bool> ValidateGroupExistenceAsync(WxCallbackDto callbackDto, Guid wxManagerId, string processingId)
    {
        var fromGroup = callbackDto.FromGroup;
        
        if (string.IsNullOrEmpty(fromGroup))
        {
            _logger.LogWarning("[{ProcessingId}] ⚠️ 8字头群聊消息但FromGroup为空，消息被跳过 - MessageType: {MessageType}, 期望FromGroup参数",
                processingId, callbackDto.Type);
            return false;
        }

        var groupExists = await _dbContext.WxGroupEntities
            .AsNoTracking()
            .AnyAsync(g => g.ChatRoomId == fromGroup && g.WxManagerId == wxManagerId);

        if (!groupExists)
        {
            _logger.LogWarning("[{ProcessingId}] ❌ 8字头群聊消息群组不存在，消息被跳过 - FromGroup: '{FromGroup}', MessageType: {MessageType}",
                processingId, fromGroup, callbackDto.Type);
            return false;
        }

        _logger.LogDebug("[{ProcessingId}] ✅ 8字头群聊消息验证通过 - FromGroup: {FromGroup}, MessageType: {MessageType}, WxManagerId: {WxManagerId}",
            processingId, fromGroup, callbackDto.Type, wxManagerId);
        return true;
    }

    /// <summary>
    /// 验证联系人存在性
    /// </summary>
    private async Task<bool> ValidateContactExistenceAsync(WxCallbackDto callbackDto, Guid wxManagerId, string processingId)
    {
        var fromUser = callbackDto.FromUser;
        
        if (string.IsNullOrEmpty(fromUser))
        {
            _logger.LogWarning("[{ProcessingId}] ⚠️ 6字头私聊消息但FromUser为空，消息被跳过 - MessageType: {MessageType}, 期望FromUser参数",
                processingId, callbackDto.Type);
            return false;
        }

        var contactExists = await _dbContext.WxContactEntities
            .AsNoTracking()
            .AnyAsync(c => c.WcId == fromUser && c.WxManagerId == wxManagerId);

        if (!contactExists)
        {
            _logger.LogWarning("[{ProcessingId}] ❌ 6字头私聊消息联系人不存在，消息被跳过 - FromUser: '{FromUser}', MessageType: {MessageType}",
                processingId, fromUser, callbackDto.Type);
            return false;
        }

        _logger.LogDebug("[{ProcessingId}] ✅ 6字头私聊消息验证通过 - FromUser: {FromUser}, MessageType: {MessageType}, WxManagerId: {WxManagerId}",
            processingId, fromUser, callbackDto.Type, wxManagerId);
        return true;
    }

    /// <summary>
    /// 🚀 流式架构：路由到智能消息路由器 - 毫秒级响应，永不阻塞
    /// 完全替代旧的队列路由系统，解决阻塞和消息丢失问题
    /// </summary>
    private async Task<bool> RouteToStreamingArchitectureAsync(WxCallbackDto callbackDto, string processingId)
    {
        var routingStart = DateTime.UtcNow;
        try
        {
            _logger.LogDebug("[{ProcessingId}] 🚀 流式架构路由开始 - MessageType: {MessageType}", 
                processingId, callbackDto.Type);

            // 验证WxManagerId
            if (!callbackDto.WxManagerId.HasValue)
            {
                _logger.LogError("[{ProcessingId}] ❌ WxManagerId为空，无法路由消息", processingId);
                return false;
            }

            // 🚀 预处理：敏感词检测（非阻塞）
            if (IsTextMessage(callbackDto.Type!) && !string.IsNullOrEmpty(callbackDto.Content))
            {
                var shouldContinue = await PreprocessTextMessageAsync(callbackDto, processingId);
                if (!shouldContinue)
                {
                    _logger.LogInformation("[{ProcessingId}] 🚫 消息被敏感词拦截，停止处理", processingId);
                    return true; // 拦截不算错误，返回true避免重试
                }
            }

            // 🚀 转换为EYun标准格式
            var eyunCallbackMessage = ConvertToEYunFormat(callbackDto, processingId);
            
            // 🚀 关键：路由到智能消息路由器 - 毫秒级响应，永不阻塞
            var routingResult = await _messageRouter.RouteMessageAsync(eyunCallbackMessage, processingId);
            
            var routingTime = DateTime.UtcNow - routingStart;
            
            if (routingResult.Success)
            {
                _logger.LogInformation("[{ProcessingId}] ✅ 流式架构路由完成 - Duration: {Duration}ms, Result: {Result}", 
                    processingId, routingTime.TotalMilliseconds, routingResult.Message);
                return true;
            }
            else
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 流式架构路由失败 - Duration: {Duration}ms, Error: {Error}", 
                    processingId, routingTime.TotalMilliseconds, routingResult.Message);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 消息路由失败 - MessageType: {MessageType}", 
                processingId, callbackDto.Type);
            return false;
        }
    }

    /// <summary>
    /// 🚀 流式架构：转换为EYun标准格式
    /// </summary>
    private WxCallbackMessageDto ConvertToEYunFormat(WxCallbackDto callbackDto, string processingId)
    {
        try
        {
            var eyunMessage = new WxCallbackMessageDto
            {
                WxManagerId = callbackDto.WxManagerId!.Value.ToString(),
                WcId = callbackDto.WcId,
                MessageType = callbackDto.Type!,
                Data = new MessageData
                {
                    WId = "", // Will be populated by downstream processors
                    FromUser = callbackDto.FromUser,
                    ToUser = callbackDto.ToUser,
                    Content = callbackDto.Content,
                    FromGroup = callbackDto.FromGroup,
                    Timestamp = callbackDto.Timestamp,
                    MsgId = callbackDto.MsgId,
                    NewMsgId = callbackDto.NewMsgId,
                    Atlist = callbackDto.Atlist
                }
            };
            
            _logger.LogDebug("[{ProcessingId}] 🔄 消息格式转换完成 - MessageType: {MessageType}", 
                processingId, callbackDto.Type);
                
            return eyunMessage;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 消息格式转换失败", processingId);
            throw;
        }
    }

    // 🚀 流式架构：媒体消息预处理已集成到MediaPreprocessor中，无需在此处理

    /// <summary>
    /// 预处理文本消息
    /// </summary>
    private async Task<bool> PreprocessTextMessageAsync(WxCallbackDto callbackDto, string processingId)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 📝 开始文本消息预处理", processingId);

            // 敏感词检测
            var sensitiveResult = await _sensitiveWordService.CheckIncomingMessageAsync(
                callbackDto.Content!,
                callbackDto.WxManagerId.ToString());

            if (sensitiveResult.ContainsSensitiveWords)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 检测到敏感词 - Count: {Count}, Action: {Action}", 
                    processingId, sensitiveResult.DetectedWords.Count, sensitiveResult.Action);

                // 根据策略处理敏感词
                switch (sensitiveResult.Action)
                {
                    case SensitiveWordActionType.Block:
                        // 阻止消息处理
                        _logger.LogWarning("[{ProcessingId}] 🚫 消息被阻止 - 包含敏感词", processingId);
                        return false; // 返回false表示消息被阻止

                    case SensitiveWordActionType.Replace:
                        // 替换敏感词
                        callbackDto.Content = sensitiveResult.ProcessedContent;
                        _logger.LogDebug("[{ProcessingId}] 🔄 敏感词已替换", processingId);
                        break;

                    case SensitiveWordActionType.LogOnly:
                        // 仅记录日志，不做处理
                        _logger.LogInformation("[{ProcessingId}] 📝 敏感词已记录 - Words: {Words}",
                            processingId, string.Join(", ", sensitiveResult.DetectedWords));
                        break;
                }
            }

            _logger.LogDebug("[{ProcessingId}] ✅ 文本消息预处理完成", processingId);
            return true; // 返回true表示消息可以继续处理
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 文本消息预处理失败", processingId);
            return true; // 异常时允许消息继续处理，避免因敏感词检测异常导致消息丢失
        }
    }

    /// <summary>
    /// 检查是否为媒体消息
    /// </summary>
    private bool IsMediaMessage(string messageType)
    {
        return messageType is 
            MessageTypeConstants.PRIVATE_IMAGE or MessageTypeConstants.GROUP_IMAGE or
            MessageTypeConstants.PRIVATE_VOICE or MessageTypeConstants.GROUP_VOICE or
            MessageTypeConstants.PRIVATE_FILE or MessageTypeConstants.GROUP_FILE;
    }

    /// <summary>
    /// 检查是否为文本消息
    /// </summary>
    private bool IsTextMessage(string messageType)
    {
        return messageType is MessageTypeConstants.PRIVATE_TEXT or MessageTypeConstants.GROUP_TEXT;
    }

    /// <summary>
    /// 将WxMessage转换为WxCallbackDto
    /// </summary>
    private WxCallbackDto ConvertMessageToDto(WxMessage message)
    {
        return new WxCallbackDto
        {
            // 注意：WxMessage实体缺少WxManagerId和WcId字段，需要从其他方式获取
            WxManagerId = Guid.Empty, // 需要根据实际业务逻辑获取
            WcId = "", // 需要根据实际业务逻辑获取
            Type = message.Type.ToString(),
            FromUser = message.FromUser,
            ToUser = message.ToUser,
            Content = message.Content,
            Timestamp = ((DateTimeOffset)message.CreatedAt).ToUnixTimeSeconds()
        };
    }
    
    /// <summary>
    /// 根据消息类型输出不同的日志格式
    /// 只有文本消息输出content，图片、文件、语音不输出content
    /// </summary>
    private void LogCallbackDataReceived(WxCallbackDto? callbackDto, string processingId)
    {
        if (callbackDto == null)
        {
            _logger.LogInformation("[{ProcessingId}] 📥 收到EYun回调数据", processingId);
            return;
        }

        var messageType = callbackDto.Type;
        
        // 判断是否为文本消息（只有60001和80001输出content）
        var isTextMessage = messageType == MessageTypeConstants.PRIVATE_TEXT || messageType == MessageTypeConstants.GROUP_TEXT;
        var content = isTextMessage && !string.IsNullOrEmpty(callbackDto.Content)
            ? (callbackDto.Content.Length > 50 ? callbackDto.Content.Substring(0, 50) + "..." : callbackDto.Content)
            : null;

        // 按消息类型区分日志输出格式
        if (messageType?.StartsWith("8") == true)
        {
            // 8字头：群聊消息
            if (isTextMessage && content != null)
            {
                _logger.LogInformation("[{ProcessingId}] 📥 收到EYun回调数据: FromGroup: {FromGroup}, Content: {Content}, MessageType: {MessageType}", 
                    processingId, callbackDto.FromGroup, content, messageType);
            }
            else
            {
                _logger.LogInformation("[{ProcessingId}] 📥 收到EYun回调数据: FromGroup: {FromGroup}, MessageType: {MessageType}", 
                    processingId, callbackDto.FromGroup, messageType);
            }
        }
        else if (messageType?.StartsWith("6") == true)
        {
            // 6字头：私聊消息
            if (isTextMessage && content != null)
            {
                _logger.LogInformation("[{ProcessingId}] 📥 收到EYun回调数据: FromUser: {FromUser}, Content: {Content}, MessageType: {MessageType}", 
                    processingId, callbackDto.FromUser, content, messageType);
            }
            else
            {
                _logger.LogInformation("[{ProcessingId}] 📥 收到EYun回调数据: FromUser: {FromUser}, MessageType: {MessageType}", 
                    processingId, callbackDto.FromUser, messageType);
            }
        }
        else
        {
            // 3字头或其他：系统消息，保持现有格式
            _logger.LogInformation("[{ProcessingId}] 📥 收到EYun回调数据: MessageType: {MessageType}", 
                processingId, messageType);
        }
    }
}