using System.Diagnostics;
using System.Text.RegularExpressions;
using HappyWechat.Application.Interfaces;
using Microsoft.Extensions.Logging;

namespace HappyWechat.Infrastructure.Services;

/// <summary>
/// URL分析服务实现 - 支持短链接转换和URL类型识别
/// </summary>
public class UrlAnalyzer : IUrlAnalyzer
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<UrlAnalyzer> _logger;
    
    // 短链接域名模式
    private static readonly HashSet<string> ShortUrlDomains = new(StringComparer.OrdinalIgnoreCase)
    {
        "bit.ly", "tinyurl.com", "t.co", "goo.gl", "ow.ly", "is.gd", "buff.ly",
        "short.link", "tiny.cc", "url.cn", "dwz.cn", "suo.im", "mrw.so",
        "t.cn", "weibo.cn", "tb.cn", "u.to", "v.ht", "x.co"
    };
    
    // 图片文件扩展名
    private static readonly HashSet<string> ImageExtensions = new(StringComparer.OrdinalIgnoreCase)
    {
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg", ".ico", ".tiff", ".tif"
    };
    
    // 文件扩展名（非图片）
    private static readonly HashSet<string> FileExtensions = new(StringComparer.OrdinalIgnoreCase)
    {
        ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt", ".rtf",
        ".zip", ".rar", ".7z", ".tar", ".gz", ".mp3", ".mp4", ".avi", ".mov", ".wmv"
    };
    
    public UrlAnalyzer(HttpClient httpClient, ILogger<UrlAnalyzer> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        
        // 配置HttpClient
        _httpClient.Timeout = TimeSpan.FromSeconds(10);
        _httpClient.DefaultRequestHeaders.Add("User-Agent", 
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
    }
    
    /// <summary>
    /// 分析URL并确定消息类型
    /// </summary>
    public async Task<UrlAnalysisResult> AnalyzeUrlAsync(string url, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            if (string.IsNullOrWhiteSpace(url))
            {
                return UrlAnalysisResult.CreateFailure(url, "URL为空");
            }
            
            // 确保URL格式正确
            if (!Uri.TryCreate(url, UriKind.Absolute, out var uri))
            {
                return UrlAnalysisResult.CreateFailure(url, "URL格式无效");
            }
            
            var isShortUrl = IsShortUrl(url);
            var resolvedUrl = url;
            
            // 如果是短链接，尝试解析
            if (isShortUrl)
            {
                var resolved = await ResolveShortUrlAsync(url, cancellationToken);
                if (!string.IsNullOrEmpty(resolved))
                {
                    resolvedUrl = resolved;
                    _logger.LogDebug("短链接解析成功 - 原始: {OriginalUrl}, 解析: {ResolvedUrl}", url, resolvedUrl);
                }
                else
                {
                    _logger.LogWarning("短链接解析失败，使用原始URL - {Url}", url);
                }
            }
            
            // 确定消息类型
            var messageType = await DetermineMessageTypeAsync(resolvedUrl, cancellationToken);
            var fileExtension = Path.GetExtension(new Uri(resolvedUrl).AbsolutePath);
            
            stopwatch.Stop();
            
            return UrlAnalysisResult.CreateSuccess(
                url, 
                resolvedUrl, 
                messageType.Type, 
                messageType.ContentType, 
                fileExtension, 
                isShortUrl, 
                stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "URL分析异常 - {Url}", url);
            return UrlAnalysisResult.CreateFailure(url, ex.Message, stopwatch.ElapsedMilliseconds);
        }
    }
    
    /// <summary>
    /// 批量分析URL
    /// </summary>
    public async Task<List<UrlAnalysisResult>> AnalyzeUrlsAsync(List<string> urls, CancellationToken cancellationToken = default)
    {
        var tasks = urls.Select(url => AnalyzeUrlAsync(url, cancellationToken));
        var results = await Task.WhenAll(tasks);
        return results.ToList();
    }
    
    /// <summary>
    /// 解析短链接获取真实URL
    /// </summary>
    public async Task<string?> ResolveShortUrlAsync(string shortUrl, CancellationToken cancellationToken = default)
    {
        try
        {
            // 使用HEAD请求避免下载完整内容
            using var request = new HttpRequestMessage(HttpMethod.Head, shortUrl);
            using var response = await _httpClient.SendAsync(request, cancellationToken);
            
            // 检查重定向
            if (response.Headers.Location != null)
            {
                var resolvedUrl = response.Headers.Location.IsAbsoluteUri 
                    ? response.Headers.Location.ToString()
                    : new Uri(new Uri(shortUrl), response.Headers.Location).ToString();
                
                _logger.LogDebug("短链接重定向解析 - {ShortUrl} -> {ResolvedUrl}", shortUrl, resolvedUrl);
                return resolvedUrl;
            }
            
            // 如果没有重定向，返回原URL
            return shortUrl;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "短链接解析失败 - {ShortUrl}", shortUrl);
            return null;
        }
    }
    
    /// <summary>
    /// 判断是否为短链接
    /// </summary>
    public bool IsShortUrl(string url)
    {
        try
        {
            if (!Uri.TryCreate(url, UriKind.Absolute, out var uri))
            {
                return false;
            }
            
            var host = uri.Host.ToLowerInvariant();
            
            // 检查是否在短链接域名列表中
            if (ShortUrlDomains.Contains(host))
            {
                return true;
            }
            
            // 检查是否为www.开头的短链接域名
            if (host.StartsWith("www."))
            {
                var domainWithoutWww = host.Substring(4);
                if (ShortUrlDomains.Contains(domainWithoutWww))
                {
                    return true;
                }
            }
            
            // 检查路径长度（短链接通常路径很短）
            var path = uri.AbsolutePath;
            if (path.Length <= 10 && Regex.IsMatch(path, @"^/[a-zA-Z0-9_-]+$"))
            {
                // 可能是短链接，但需要更多验证
                return ShortUrlDomains.Contains(host) || host.Length <= 15;
            }
            
            return false;
        }
        catch
        {
            return false;
        }
    }
    
    /// <summary>
    /// 根据URL确定消息类型
    /// </summary>
    public string DetermineMessageType(string url, string? contentType = null)
    {
        try
        {
            // 优先使用Content-Type判断
            if (!string.IsNullOrEmpty(contentType))
            {
                if (contentType.StartsWith("image/", StringComparison.OrdinalIgnoreCase))
                {
                    return "image";
                }
                
                if (contentType.StartsWith("application/") || 
                    contentType.StartsWith("text/") && !contentType.Contains("html"))
                {
                    return "file";
                }
            }
            
            // 根据文件扩展名判断
            var extension = Path.GetExtension(new Uri(url).AbsolutePath);
            if (!string.IsNullOrEmpty(extension))
            {
                if (ImageExtensions.Contains(extension))
                {
                    return "image";
                }
                
                if (FileExtensions.Contains(extension))
                {
                    return "file";
                }
            }
            
            // 默认为文本类型
            return "text";
        }
        catch
        {
            return "text";
        }
    }
    
    /// <summary>
    /// 通过HTTP HEAD请求确定消息类型
    /// </summary>
    private async Task<(string Type, string? ContentType)> DetermineMessageTypeAsync(string url, CancellationToken cancellationToken)
    {
        try
        {
            // 先根据URL扩展名快速判断
            var quickType = DetermineMessageType(url);
            if (quickType != "text")
            {
                return (quickType, null);
            }
            
            // 使用HEAD请求获取Content-Type
            using var request = new HttpRequestMessage(HttpMethod.Head, url);
            using var response = await _httpClient.SendAsync(request, cancellationToken);
            
            var contentType = response.Content.Headers.ContentType?.MediaType;
            var finalType = DetermineMessageType(url, contentType);
            
            return (finalType, contentType);
        }
        catch (Exception ex)
        {
            _logger.LogDebug("无法通过HTTP请求确定URL类型，使用默认判断 - {Url}, Error: {Error}", url, ex.Message);
            return (DetermineMessageType(url), null);
        }
    }
}
