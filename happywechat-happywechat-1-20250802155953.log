[ENTRYPOINT] HappyWechat 容器启动脚本开始执行
[ENTRYPOINT] 当前用户: root (UID: 0)
[ENTRYPOINT] 以root用户运行，将修复权限后切换到应用用户
[ENTRYPOINT] 执行权限修复流程...
[ENTRYPOINT] 以root身份修复权限...
[ENTRYPOINT] 创建必要目录...
[ENTRYPOINT] 创建目录: /app/logs
[SUCCESS] 目录创建完成
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys
[ENTRYPOINT] 数据保护备份目录已创建
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys/escrow
[SUCCESS] 权限修复完成
[ENTRYPOINT] 切换到应用用户并启动应用
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建数据库连接字符串: Server=172.19.0.2;Port=3306;Database=huakai
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建Redis连接字符串: 172.19.0.3:6379
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      开始执行ID映射缓存预热...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      开始预热活跃账号缓存...
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式消息处理架构启动 - 三通道异步流水线模式
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Priority 通道处理器 - 并发度: 4
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Fast 通道处理器 - 并发度: 8
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Slow 通道处理器 - 并发度: 16
info: HappyWechat.Infrastructure.MessageQueue.MessageQueueMonitorService[0]
      🔍 消息队列监控服务启动
info: HappyWechat.Infrastructure.MessageProcessing.Monitoring.PipelineMonitor[0]
      🔍 流水线监控服务启动
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      🚀 统一EYun发送队列消费者启动
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🚀 统一消息消费者启动 - 账号级别隔离并行处理模式，负责处理AI请求和媒体请求队列
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      缓存预热完成 - 账号数: 1, 耗时: 1596ms
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      ID映射缓存预热完成 - 账号数: 1, 耗时: 2069ms
info: HappyWechat.Infrastructure.ServiceRegistration.ServiceHealthValidator[0]
      ✅ 启动验证成功完成，耗时: 1.9438ms
warn: Microsoft.AspNetCore.Hosting.Diagnostics[15]
      Overriding HTTP_PORTS '8080' and HTTPS_PORTS ''. Binding to values defined by URLS instead 'http://+:5215'.
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [e3ecf490] 📥 收到EYun回调数据: FromUser: wxid_pbqye5z48yzm22, MessageType: 60004
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: e3ecf490, StreamingType: MediaSlow, OriginalType: 60004
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理媒体消息 - ProcessingId: e3ecf490, MessageType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [e3ecf490] 🚀 统一消息处理器开始处理 - MessageType: 60004, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Slow, ProcessingId: e3ecf490, MessageType: 60004, StreamingType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [e3ecf490] ✅ 流式架构路由完成 - Duration: 30.6525ms, Result: 消息已路由到MediaSlow通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [e3ecf490] ✅ 回调数据处理完成 - MessageType: 60004, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [44d1dbfa] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [e3ecf490] 📁 媒体处理请求已入队 - MessageId: af0e21fd-0215-4b93-a317-d01de3f1515e, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, MessageType: 60004
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [e3ecf490] ✅ 统一消息处理完成 - Category: PrivateMedia, Duration: 54.5923ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构媒体消息处理结果 - ProcessingId: e3ecf490, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Slow-0, ProcessingId: e3ecf490, MessageType: MediaSlow, Duration: 61.0801ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [e3ecf490] 📁 开始媒体消息AI处理 - MessageType: 60004, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [df6a10b7] 🎵 开始处理语音消息 - MsgId: 166404436, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
warn: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [df6a10b7] 原始回调数据中的语音参数无效 - Length: 0, BufId: (null)
warn: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [df6a10b7] 回调数据参数提取失败，尝试XML解析
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      🔧 开始解析语音消息XML - Content: <msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="916" length="1011" bufid="0" aeskey="eecfe9ffd0918e60b519f6bd8a18e673" voiceurl="3052020100044b3049020100020462ca6be902032f533f02045c80336f02046890857c042464653864663761362d623935662d346430662d613838382d3231353837
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      ✅ 语音XML解析成功 - Length: 1011, BufId: 0, VoiceLength: 916
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      ✅ 语音消息XML解析成功 - Length: 1011, BufId: 0, Source: XML解析-voicemsg标签
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [df6a10b7] ✅ 语音参数验证通过 - Length: 1011, BufId: 0
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [df6a10b7] 📋 语音参数提取成功 - Length: 1011, BufId: 0, Source: XML解析-voicemsg标签
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [df6a10b7] 🔧 构建EYun语音下载请求 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: 166404436, Length: 1011, BufId: 0, FromUser: wxid_pbqye5z48yzm22
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [df6a10b7] 📡 调用EYun语音下载API - Length: 1011, BufId: 0
info: HappyWechat.Infrastructure.EYun.EYunDownloadWrapper[0]
      EYun语音下载API调用 - Endpoint: /getMsgVoice, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: 166404436, Length: 1011, BufId: 0
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [df6a10b7] ✅ EYun语音下载API调用成功
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [df6a10b7] ✅ EYun语音下载链接获取成功 - URL: http://wxapii.oos-hazz.ctyunapi.cn/20250804/wxid_ic3nmv9anggz22/bf143479-69db-44b5-a2d9-e93a76d47696.silk?AWSAccessKeyId=9e882e7187c38b431303&Expires=**********&Signature=c%2BqMJQN5KRMaUxya4BS11Wr%2FuVA%3D
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      MinIO文件上传成功 - Path: wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/voice_166404436_20250804180342.mp3, Size: 10702 bytes
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [df6a10b7] ✅ 语音处理完成 - 文件: voice_166404436_20250804180342.mp3, URL: http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/voice_166404436_20250804180342.mp3, 大小: 10702字节
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [e3ecf490] ✅ 媒体处理完成 - URL: http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/voice_166404436_20250804180342.mp3
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [e3ecf490] 📝 AI请求已入队 - MessageId: 7edbdb09-979e-4350-a970-5eee082606e3, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 7
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [e3ecf490] ✅ 媒体消息AI处理完成 - Duration: 1898.2729ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [e3ecf490] ✅ 媒体请求处理完成 - AiProcessed: True
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      📱 账号媒体消息处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_pbqye5z48yzm22，语音消息，媒体文件链接：http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/voice_166404436_20250804180342.mp3
warn: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 会话不存在，尝试创建新会话 - ConversationId: 256b2ee4-405b-4763-ade8-a0e078ff8ed0
info: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 重试请求（创建新会话）
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_pbqye5z48yzm22，语音消息，媒体文件链接：http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/voice_166404436_20250804180342.mp3
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [a5f1fcc3] 🤖 开始统一AI响应处理 - Length: 55, ToUser: wxid_pbqye5z48yzm22, ToGroup: (null)
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [a5f1fcc3] 📝 AI响应拆分完成 - 原始长度: 55, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: 39ab0f9e, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: 39ab0f9e, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [a5f1fcc3] ✅ AI响应已入队统一发送队列 - BatchId: 39ab0f9e, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [e3ecf490] ✅ AI请求处理完成 - BatchId: 39ab0f9e, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [af0013d8] 📥 收到EYun回调数据: FromUser: wxid_pbqye5z48yzm22, MessageType: 60002
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Slow, ProcessingId: af0013d8, MessageType: 60002, StreamingType: MediaSlow
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: af0013d8, StreamingType: MediaSlow, OriginalType: 60002
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理媒体消息 - ProcessingId: af0013d8, MessageType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [af0013d8] 🚀 统一消息处理器开始处理 - MessageType: 60002, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [af0013d8] ✅ 流式架构路由完成 - Duration: 11.9936ms, Result: 消息已路由到MediaSlow通道
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [af0013d8] 📁 媒体处理请求已入队 - MessageId: 4841d8a8-34bf-4774-974f-88949138d0fe, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, MessageType: 60002
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [af0013d8] ✅ 统一消息处理完成 - Category: PrivateMedia, Duration: 1.6166ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构媒体消息处理结果 - ProcessingId: af0013d8, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Slow-1, ProcessingId: af0013d8, MessageType: MediaSlow, Duration: 2.1897ms
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [af0013d8] ✅ 回调数据处理完成 - MessageType: 60002, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [bcba9104] ✅ 回调处理成功
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      ✅ SessionId获取成功 - 策略: HTTP上下文策略, SessionId: 9ce43737..., 耗时: 2ms
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [d7f70f8f] 📥 收到EYun回调数据: FromUser: wxid_pbqye5z48yzm22, MessageType: 60008
info: HappyWechat.Web.Controllers.WxController[0]
      [fe6ef162] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [c2555cae] 📥 收到EYun回调数据: FromUser: wxid_pbqye5z48yzm22, MessageType: 60009
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Slow, ProcessingId: c2555cae, MessageType: 60009, StreamingType: MediaSlow
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: c2555cae, StreamingType: MediaSlow, OriginalType: 60009
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理媒体消息 - ProcessingId: c2555cae, MessageType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [c2555cae] ✅ 流式架构路由完成 - Duration: 7.391ms, Result: 消息已路由到MediaSlow通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [c2555cae] ✅ 回调数据处理完成 - MessageType: 60009, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [e3f93c43] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [c2555cae] 🚀 统一消息处理器开始处理 - MessageType: 60009, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [c2555cae] 📁 媒体处理请求已入队 - MessageId: 694b042f-7b6b-49b4-a428-5b43449aba0e, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, MessageType: 60009
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [c2555cae] ✅ 统一消息处理完成 - Category: PrivateMedia, Duration: 2.4592ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构媒体消息处理结果 - ProcessingId: c2555cae, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Slow-3, ProcessingId: c2555cae, MessageType: MediaSlow, Duration: 3.9954ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [c2555cae] 📁 开始媒体消息AI处理 - MessageType: 60009, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.EYun.EYunDownloadWrapper[0]
      EYun文件下载API调用 - Endpoint: /getMsgFile, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: **********
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      通过MemoryStream获取流大小: 447 bytes - wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/file_**********_20250804180427.txt
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      MinIO文件上传成功 - Path: wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/file_**********_20250804180427.txt, Size: 447 bytes
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      文件下载并存储成功 - URL: http://**************:35684/wechat/wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/file_**********_20250804180427.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=lRH2411C25mhOJ4X8Qy4%2F20250804%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250804T100427Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=c94ceb3ff3b88926bb0de96e0abc5fba0a9a8597de5904eb8974c37ff7e330f5, Size: 447 bytes - file_**********_20250804180427.txt
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [c2555cae] ✅ 媒体处理完成 - URL: http://**************:35684/wechat/wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/file_**********_20250804180427.txt
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [c2555cae] 📝 AI请求已入队 - MessageId: 5c802565-07ca-4dd9-a1f8-00f44e097205, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 3
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [c2555cae] ✅ 媒体消息AI处理完成 - Duration: 730.6554ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [c2555cae] ✅ 媒体请求处理完成 - AiProcessed: True
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      📱 账号媒体消息处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_pbqye5z48yzm22，文件消息，媒体文件链接：http://**************:35684/wechat/wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/file_**********_20250804180427.txt
warn: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 会话不存在，尝试创建新会话 - ConversationId: c07d47fa-ce72-436d-ac2e-1317b8ee1431
info: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 重试请求（创建新会话）
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_pbqye5z48yzm22，文件消息，媒体文件链接：http://**************:35684/wechat/wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/file_**********_20250804180427.txt
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [e79e3956] 🤖 开始统一AI响应处理 - Length: 63, ToUser: wxid_pbqye5z48yzm22, ToGroup: (null)
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [e79e3956] 📝 AI响应拆分完成 - 原始长度: 63, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: e0c5434a, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: e0c5434a, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [e79e3956] ✅ AI响应已入队统一发送队列 - BatchId: e0c5434a, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [c2555cae] ✅ AI请求处理完成 - BatchId: e0c5434a, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [ef6a6a98] 📥 收到EYun回调数据: FromUser: wxid_pbqye5z48yzm22, Content: 你好, MessageType: 60001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Fast, ProcessingId: ef6a6a98, MessageType: 60001, StreamingType: TextFast
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: ef6a6a98, StreamingType: TextFast, OriginalType: 60001
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [ef6a6a98] ✅ 流式架构路由完成 - Duration: 30.6948ms, Result: 消息已路由到PrivateTextFast通道
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理文本消息 - ProcessingId: ef6a6a98, MessageType: TextFast
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [ef6a6a98] 🚀 统一消息处理器开始处理 - MessageType: 60001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [ef6a6a98] ✅ 回调数据处理完成 - MessageType: 60001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [19077674] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [ef6a6a98] 📝 AI请求已入队 - MessageId: 0cbdade7-f986-4909-bc95-1924ce351cd6, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 8
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [ef6a6a98] ✅ 统一消息处理完成 - Category: PrivateText, Duration: 11.7644ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构文本消息处理结果 - ProcessingId: ef6a6a98, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Fast-0, ProcessingId: ef6a6a98, MessageType: TextFast, Duration: 13.4731ms
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_pbqye5z48yzm22，文本消息内容：你好
warn: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 会话不存在，尝试创建新会话 - ConversationId: acf75af5-eae1-4d86-9e29-b5ea9a01b810
info: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 重试请求（创建新会话）
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_pbqye5z48yzm22，文本消息内容：你好
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [6854ea9b] 🤖 开始统一AI响应处理 - Length: 14, ToUser: wxid_pbqye5z48yzm22, ToGroup: (null)
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [6854ea9b] 📝 AI响应拆分完成 - 原始长度: 14, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: acc3fd61, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: acc3fd61, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [6854ea9b] ✅ AI响应已入队统一发送队列 - BatchId: acc3fd61, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [ef6a6a98] ✅ AI请求处理完成 - BatchId: acc3fd61, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [0d0d1b2d] 📥 收到EYun回调数据: FromGroup: 53451126890@chatroom, Content: @心 你好, MessageType: 80001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Priority, ProcessingId: 0d0d1b2d, MessageType: 80001, StreamingType: PriorityAt
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [0d0d1b2d] ✅ 流式架构路由完成 - Duration: 12.8485ms, Result: 消息已路由到PriorityAtMessage通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [0d0d1b2d] ✅ 回调数据处理完成 - MessageType: 80001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [570d25ed] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: 0d0d1b2d, StreamingType: PriorityAt, OriginalType: 80001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理优先级消息 - ProcessingId: 0d0d1b2d, MessageType: PriorityAt
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [0d0d1b2d] 🚀 统一消息处理器开始处理 - MessageType: 80001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 检查群消息AI回复触发条件 - MessageType: 80001, Group: 53451126890@chatroom, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      📋 群组配置检查 - GroupName: 花开了, IsAiEnabled: True, AiAgentId: a657e9fa-809b-4a7a-9c76-74dec4277ba7
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 开始@检测 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], Content: '@心 你好'
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ✅ @检测成功 - 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 @状态检查完成 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], IsMentioned: True, Method: 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      📝 应用@提及规则 - OnlyReplyWhenMentioned: True, IsMentioned: True
info: HappyWechat.Infrastructure.MessageProcessing.Services.MessageCombinationService[0]
      [f4d46eea] 🔄 消息组合处理 - MessageType: 80001, FromGroup: 53451126890@chatroom
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ⚡ 群组@模式 - 消息立即处理：单独文本消息 - ProcessingId: f4d46eea
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 开始@检测 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], Content: '@心 你好'
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ✅ @检测成功 - 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 @状态检查完成 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], IsMentioned: True, Method: 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 群消息处理结果 - MessageType: 80001, OnlyReplyWhenMentioned: True, IsMentioned: True, ShouldReply: True, HasCombinedMessage: False
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [0d0d1b2d] 📝 AI请求已入队 - MessageId: 2bd56c3e-ef58-4817-a82b-f14c59223b37, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 10
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [0d0d1b2d] ✅ 统一消息处理完成 - Category: GroupText, Duration: 61.3277ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ⚡ 流式架构优先级消息处理完成 - ProcessingId: 0d0d1b2d, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Priority-0, ProcessingId: 0d0d1b2d, MessageType: PriorityAt, Duration: 63.2856ms
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_pbqye5z48yzm22,FromGroup: 53451126890@chatroom,FromGroupUser: wxid_pbqye5z48yzm22,SenderNickname: 浮生无垠，群聊文本消息内容：@心 你好
warn: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 会话不存在，尝试创建新会话 - ConversationId: 900bdf6d-ce1f-4dcc-9241-3ee1ad4215ba
info: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 重试请求（创建新会话）
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_pbqye5z48yzm22,FromGroup: 53451126890@chatroom,FromGroupUser: wxid_pbqye5z48yzm22,SenderNickname: 浮生无垠，群聊文本消息内容：@心 你好
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [15ddbe23] 🤖 开始统一AI响应处理 - Length: 14, ToUser: wxid_pbqye5z48yzm22, ToGroup: 53451126890@chatroom
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [15ddbe23] 📝 AI响应拆分完成 - 原始长度: 14, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: a06081d6, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: a06081d6, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [15ddbe23] ✅ AI响应已入队统一发送队列 - BatchId: a06081d6, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [0d0d1b2d] ✅ AI请求处理完成 - BatchId: a06081d6, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Web.Services.ContactAiConfigCacheService[0]
      联系人AI配置缓存失效完成 - ContactId: 8eff361d-c6ff-476c-be53-d8a560a2ef10
info: HappyWechat.Web.Services.ContactAiConfigCacheService[0]
      所有联系人AI配置缓存失效完成
info: HappyWechat.Web.Controllers.WxController[0]
      ✅ 联系人AI配置缓存已清除 - ContactId: 8eff361d-c6ff-476c-be53-d8a560a2ef10, ManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      ✅ 联系人AI配置变更通知已记录 - ContactId: 8eff361d-c6ff-476c-be53-d8a560a2ef10
info: HappyWechat.Infrastructure.Integration.UnifiedArchitectureIntegrationService[0]
      Clearing account cache for 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Components.Common.BaseWxPageComponent[0]
      ✅ AI配置更新成功 - Contact: 8eff361d-c6ff-476c-be53-d8a560a2ef10
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [d6419189] 📥 收到EYun回调数据: FromUser: wxid_pbqye5z48yzm22, MessageType: 60004
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Slow, ProcessingId: d6419189, MessageType: 60004, StreamingType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [d6419189] ✅ 流式架构路由完成 - Duration: 6.4275ms, Result: 消息已路由到MediaSlow通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [d6419189] ✅ 回调数据处理完成 - MessageType: 60004, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: d6419189, StreamingType: MediaSlow, OriginalType: 60004
info: HappyWechat.Web.Controllers.WxController[0]
      [450854cc] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理媒体消息 - ProcessingId: d6419189, MessageType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [d6419189] 🚀 统一消息处理器开始处理 - MessageType: 60004, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [d6419189] 📁 媒体处理请求已入队 - MessageId: a9fe9b19-6e47-4399-bee5-70023d623796, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, MessageType: 60004
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [d6419189] ✅ 统一消息处理完成 - Category: PrivateMedia, Duration: 2.4631ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构媒体消息处理结果 - ProcessingId: d6419189, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Slow-4, ProcessingId: d6419189, MessageType: MediaSlow, Duration: 2.647ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [d6419189] 📁 开始媒体消息AI处理 - MessageType: 60004, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [5604026f] 🎵 开始处理语音消息 - MsgId: 1884419777, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
warn: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [5604026f] 原始回调数据中的语音参数无效 - Length: 0, BufId: (null)
warn: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [5604026f] 回调数据参数提取失败，尝试XML解析
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      🔧 开始解析语音消息XML - Content: <msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="894" length="1057" bufid="0" aeskey="62e9ab6aa7fcdb78821cf83a85748349" voiceurl="3052020100044b3049020100020462ca6be902032f533f02045c80336f02046890861c042435333262313761642d383332312d343463352d616261612d6634383035
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      ✅ 语音XML解析成功 - Length: 1057, BufId: 0, VoiceLength: 894
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      ✅ 语音消息XML解析成功 - Length: 1057, BufId: 0, Source: XML解析-voicemsg标签
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [5604026f] ✅ 语音参数验证通过 - Length: 1057, BufId: 0
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [5604026f] 📋 语音参数提取成功 - Length: 1057, BufId: 0, Source: XML解析-voicemsg标签
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [5604026f] 🔧 构建EYun语音下载请求 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: 1884419777, Length: 1057, BufId: 0, FromUser: wxid_pbqye5z48yzm22
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [5604026f] 📡 调用EYun语音下载API - Length: 1057, BufId: 0
info: HappyWechat.Infrastructure.EYun.EYunDownloadWrapper[0]
      EYun语音下载API调用 - Endpoint: /getMsgVoice, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: 1884419777, Length: 1057, BufId: 0
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [5604026f] ✅ EYun语音下载API调用成功
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [5604026f] ✅ EYun语音下载链接获取成功 - URL: http://wxapii.oos-hazz.ctyunapi.cn/20250804/wxid_ic3nmv9anggz22/a269693b-ca0e-405d-966e-fc317a420085.silk?AWSAccessKeyId=9e882e7187c38b431303&Expires=**********&Signature=09M3lli%2Fvh%2FfSgcvi8yJGu2VjMM%3D
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      MinIO文件上传成功 - Path: wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/voice_1884419777_20250804180621.mp3, Size: 10702 bytes
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [5604026f] ✅ 语音处理完成 - 文件: voice_1884419777_20250804180621.mp3, URL: http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/voice_1884419777_20250804180621.mp3, 大小: 10702字节
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [d6419189] ✅ 媒体处理完成 - URL: http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/voice_1884419777_20250804180621.mp3
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [d6419189] 📝 AI请求已入队 - MessageId: af303576-cae6-4b8f-8178-613398c2a9ee, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 7
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [d6419189] ✅ 媒体消息AI处理完成 - Duration: 839.126ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [d6419189] ✅ 媒体请求处理完成 - AiProcessed: True
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      📱 账号媒体消息处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_pbqye5z48yzm22，语音消息，媒体文件链接：http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/voice_1884419777_20250804180621.mp3
warn: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 会话不存在，尝试创建新会话 - ConversationId: 65b89b47-3d4c-4cc2-873f-02b507bac9c1
info: HappyWechat.Infrastructure.AiProvider.DifyAiProvider[0]
      [Dify] 重试请求（创建新会话）
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [Dify] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_pbqye5z48yzm22，语音消息，媒体文件链接：http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/voice_1884419777_20250804180621.mp3
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [9a0782c7] 🤖 开始统一AI响应处理 - Length: 62, ToUser: wxid_pbqye5z48yzm22, ToGroup: (null)
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [9a0782c7] 📝 AI响应拆分完成 - 原始长度: 62, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: 68d70f13, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: 68d70f13, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [9a0782c7] ✅ AI响应已入队统一发送队列 - BatchId: 68d70f13, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [d6419189] ✅ AI请求处理完成 - BatchId: 68d70f13, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📊 流式架构状态报告 - Queues: Fast(0), Slow(0), Priority(0) | Stats: Processed(6), Failed(0), Enqueued(6)
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📊 流式架构状态报告 - Queues: Fast(0), Slow(0), Priority(0) | Stats: Processed(6), Failed(0), Enqueued(6)
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📊 流式架构状态报告 - Queues: Fast(0), Slow(0), Priority(0) | Stats: Processed(6), Failed(0), Enqueued(6)
