[ENTRYPOINT] HappyWechat 容器启动脚本开始执行
[ENTRYPOINT] 当前用户: root (UID: 0)
[ENTRYPOINT] 以root用户运行，将修复权限后切换到应用用户
[ENTRYPOINT] 执行权限修复流程...
[ENTRYPOINT] 以root身份修复权限...
[ENTRYPOINT] 创建必要目录...
[ENTRYPOINT] 创建目录: /app/logs
[SUCCESS] 目录创建完成
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys
[ENTRYPOINT] 数据保护备份目录已创建
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys/escrow
[SUCCESS] 权限修复完成
[ENTRYPOINT] 切换到应用用户并启动应用
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建数据库连接字符串: Server=172.19.0.2;Port=3306;Database=huakai
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建Redis连接字符串: 172.19.0.3:6379
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      开始执行ID映射缓存预热...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      开始预热活跃账号缓存...
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式消息处理架构启动 - 三通道异步流水线模式
info: HappyWechat.Infrastructure.MessageQueue.MessageQueueMonitorService[0]
      🔍 消息队列监控服务启动
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Fast 通道处理器 - 并发度: 8
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Priority 通道处理器 - 并发度: 4
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Slow 通道处理器 - 并发度: 16
info: HappyWechat.Infrastructure.MessageProcessing.Monitoring.PipelineMonitor[0]
      🔍 流水线监控服务启动
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      🚀 统一EYun发送队列消费者启动
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🚀 统一消息消费者启动 - 账号级别隔离并行处理模式，负责处理AI请求和媒体请求队列
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      缓存预热完成 - 账号数: 1, 耗时: 1497ms
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      ID映射缓存预热完成 - 账号数: 1, 耗时: 1915ms
info: HappyWechat.Infrastructure.ServiceRegistration.ServiceHealthValidator[0]
      ✅ 启动验证成功完成，耗时: 2.0463ms
warn: Microsoft.AspNetCore.Hosting.Diagnostics[15]
      Overriding HTTP_PORTS '8080' and HTTPS_PORTS ''. Binding to values defined by URLS instead 'http://+:5215'.
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [d9840223] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, MessageType: 60004
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Slow, ProcessingId: d9840223, MessageType: 60004, StreamingType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [d9840223] ✅ 流式架构路由完成 - Duration: 22.8289ms, Result: 消息已路由到MediaSlow通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [d9840223] ✅ 回调数据处理完成 - MessageType: 60004, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: d9840223, StreamingType: MediaSlow, OriginalType: 60004
info: HappyWechat.Web.Controllers.WxController[0]
      [9c06af77] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理媒体消息 - ProcessingId: d9840223, MessageType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [d9840223] 🚀 统一消息处理器开始处理 - MessageType: 60004, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [d9840223] 📁 媒体处理请求已入队 - MessageId: 3169a864-ef39-4ef8-8898-03b3b61ad9ca, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, MessageType: 60004
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [d9840223] ✅ 统一消息处理完成 - Category: PrivateMedia, Duration: 48.7718ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构媒体消息处理结果 - ProcessingId: d9840223, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Slow-1, ProcessingId: d9840223, MessageType: MediaSlow, Duration: 55.707ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [d9840223] 📁 开始媒体消息AI处理 - MessageType: 60004, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [d23bc436] 🎵 开始处理语音消息 - MsgId: 1795871970, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
warn: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [d23bc436] 原始回调数据中的语音参数无效 - Length: 0, BufId: (null)
warn: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [d23bc436] 回调数据参数提取失败，尝试XML解析
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      🔧 开始解析语音消息XML - Content: <msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1111" length="1653" bufid="0" aeskey="4c336c57e7a905734f12be3341588860" voiceurl="3052020100044b30490201000204857b835a02032f533f0204ea80336f020468907839042439663763336262302d623361302d346235372d383135642d386462316
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      ✅ 语音XML解析成功 - Length: 1653, BufId: 0, VoiceLength: 1111
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      ✅ 语音消息XML解析成功 - Length: 1653, BufId: 0, Source: XML解析-voicemsg标签
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [d23bc436] ✅ 语音参数验证通过 - Length: 1653, BufId: 0
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [d23bc436] 📋 语音参数提取成功 - Length: 1653, BufId: 0, Source: XML解析-voicemsg标签
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [d23bc436] 🔧 构建EYun语音下载请求 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: 1795871970, Length: 1653, BufId: 0, FromUser: wxid_scqpt8dyuxqv41
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [d23bc436] 📡 调用EYun语音下载API - Length: 1653, BufId: 0
info: HappyWechat.Infrastructure.EYun.EYunDownloadWrapper[0]
      EYun语音下载API调用 - Endpoint: /getMsgVoice, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: 1795871970, Length: 1653, BufId: 0
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [d23bc436] ✅ EYun语音下载API调用成功
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [d23bc436] ✅ EYun语音下载链接获取成功 - URL: http://wxapii.oos-hazz.ctyunapi.cn/20250804/wxid_ic3nmv9anggz22/5ac3a214-8ed3-4f7d-8fae-a9a85c863ea9.silk?AWSAccessKeyId=9e882e7187c38b431303&Expires=1754903227&Signature=OeSlSc2JLBnpzlsvC70qHVVjotY%3D
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      ✅ SessionId获取成功 - 策略: HTTP上下文策略, SessionId: 9ce43737..., 耗时: 2ms
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      MinIO文件上传成功 - Path: wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/voice_1795871970_20250804170707.mp3, Size: 14463 bytes
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [d23bc436] ✅ 语音处理完成 - 文件: voice_1795871970_20250804170707.mp3, URL: http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/voice_1795871970_20250804170707.mp3, 大小: 14463字节
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [d9840223] ✅ 媒体处理完成 - URL: http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/voice_1795871970_20250804170707.mp3
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [d9840223] 📝 AI请求已入队 - MessageId: 57adf40d-833f-4206-a77d-5f800ef52452, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 7
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [d9840223] ✅ 媒体消息AI处理完成 - Duration: 2408.133ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [d9840223] ✅ 媒体请求处理完成 - AiProcessed: True
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      📱 账号媒体消息处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [ChatGPT] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_scqpt8dyuxqv41，语音消息，媒体文件链接：http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/voice_1795871970_20250804170707.mp3，附加内容：<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1111" length="1653" bufid="0" aeskey="4c336c57e7a905734f12be3341588860" voiceurl="3052020100044b30490201000204857b835a02032f533f0204ea80336f020468907839042439663763336262302d623361302d346235372d383135642d38646231666131633661313602040514000f0201000400" voicemd5="" clientmsgid="41373964316163323964306135376500031707080425ea5b2c50df5106" fromusername="wxid_scqpt8dyuxqv41" /></msg>
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [679359c5] 🤖 开始统一AI响应处理 - Length: 43, ToUser: wxid_scqpt8dyuxqv41, ToGroup: (null)
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [679359c5] 📝 AI响应拆分完成 - 原始长度: 43, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: 18ac7cd3, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: 18ac7cd3, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [679359c5] ✅ AI响应已入队统一发送队列 - BatchId: 18ac7cd3, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [d9840223] ✅ AI请求处理完成 - BatchId: 18ac7cd3, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
warn: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      ⚠️ 未知的命令类型 - CommandType: <>f__AnonymousType66`5
warn: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ❌ 发送消息已标记为失败 - MessageId: 55948748-e7c1-435b-986d-1781f10eaa58, Error: 未知的命令类型
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [6cf60047] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, Content: 你好, MessageType: 60001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Fast, ProcessingId: 6cf60047, MessageType: 60001, StreamingType: TextFast
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [6cf60047] ✅ 流式架构路由完成 - Duration: 33.918ms, Result: 消息已路由到PrivateTextFast通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [6cf60047] ✅ 回调数据处理完成 - MessageType: 60001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [ff274b2a] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: 6cf60047, StreamingType: TextFast, OriginalType: 60001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理文本消息 - ProcessingId: 6cf60047, MessageType: TextFast
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [6cf60047] 🚀 统一消息处理器开始处理 - MessageType: 60001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [6cf60047] 📝 AI请求已入队 - MessageId: 472b5070-504c-41b8-a9bf-e9acd13b046e, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 8
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [6cf60047] ✅ 统一消息处理完成 - Category: PrivateText, Duration: 12.7326ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构文本消息处理结果 - ProcessingId: 6cf60047, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Fast-0, ProcessingId: 6cf60047, MessageType: TextFast, Duration: 14.8962ms
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [ChatGPT] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_scqpt8dyuxqv41，文本消息内容：你好
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [f910892c] 🤖 开始统一AI响应处理 - Length: 40, ToUser: wxid_scqpt8dyuxqv41, ToGroup: (null)
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [f910892c] 📝 AI响应拆分完成 - 原始长度: 40, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: e801ea69, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: e801ea69, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [f910892c] ✅ AI响应已入队统一发送队列 - BatchId: e801ea69, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [6cf60047] ✅ AI请求处理完成 - BatchId: e801ea69, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
warn: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      ⚠️ 未知的命令类型 - CommandType: <>f__AnonymousType66`5
warn: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ❌ 发送消息已标记为失败 - MessageId: c68eda9c-030b-468e-bc32-12184fead118, Error: 未知的命令类型
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [f5a55187] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, MessageType: 60002
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: f5a55187, StreamingType: MediaSlow, OriginalType: 60002
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理媒体消息 - ProcessingId: f5a55187, MessageType: MediaSlow
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Slow, ProcessingId: f5a55187, MessageType: 60002, StreamingType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [f5a55187] ✅ 流式架构路由完成 - Duration: 8.9476ms, Result: 消息已路由到MediaSlow通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [f5a55187] ✅ 回调数据处理完成 - MessageType: 60002, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [7ef994d1] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [f5a55187] 🚀 统一消息处理器开始处理 - MessageType: 60002, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [f5a55187] 📁 媒体处理请求已入队 - MessageId: 889e019f-6365-433b-a223-ad7628465601, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, MessageType: 60002
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [f5a55187] ✅ 统一消息处理完成 - Category: PrivateMedia, Duration: 1.4168ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构媒体消息处理结果 - ProcessingId: f5a55187, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Slow-0, ProcessingId: f5a55187, MessageType: MediaSlow, Duration: 2.5202ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [f5a55187] 📁 开始媒体消息AI处理 - MessageType: 60002, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.EYun.EYunDownloadWrapper[0]
      EYun图片下载API调用 - Endpoint: /getMsgImg, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: **********, Type: 0, ContentLength: 1279
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      通过MemoryStream获取流大小: 273787 bytes - wx-images/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/image_**********_20250804170735.jpg
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      MinIO文件上传成功 - Path: wx-images/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/image_**********_20250804170735.jpg, Size: 273787 bytes
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      文件下载并存储成功 - URL: http://**************:35684/wechat/wx-images/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/image_**********_20250804170735.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=lRH2411C25mhOJ4X8Qy4%2F20250804%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250804T090735Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=550296fd770385d5c2dab610d8d1e23bc86949dd619d7a0461316dc292ffe442, Size: 273787 bytes - image_**********_20250804170735.jpg
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [f5a55187] ✅ 媒体处理完成 - URL: http://**************:35684/wechat/wx-images/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/image_**********_20250804170735.jpg
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [f5a55187] 📝 AI请求已入队 - MessageId: f8b6237d-875f-442c-91ae-9d31332cd45d, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 5
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [f5a55187] ✅ 媒体消息AI处理完成 - Duration: 947.1408ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [f5a55187] ✅ 媒体请求处理完成 - AiProcessed: True
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      📱 账号媒体消息处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [ChatGPT] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_scqpt8dyuxqv41，图片消息，媒体文件链接：http://**************:35684/wechat/wx-images/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/image_**********_20250804170735.jpg，附加内容：<?xml version="1.0"?>
      <msg>
      	<img aeskey="dcdc508db5ac65565dd0a91e0e1dc3d2" encryver="1" cdnthumbaeskey="dcdc508db5ac65565dd0a91e0e1dc3d2" cdnthumburl="3057020100044b30490201000204114898ab02032f533f02049780336f020468904bc3042466346665666533302d383965332d346233622d396564642d356636636566656139336665020405150a020201000405004c543f00" cdnthumblength="3032" cdnthumbheight="433" cdnthumbwidth="245" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204114898ab02032f533f02049780336f020468904bc3042466346665666533302d383965332d346233622d396564642d356636636566656139336665020405150a020201000405004c543f00" length="273787" md5="6c1956a98d2be9fc218359ba68bae2a1" hevc_mid_size="9296" originsourcemd5="2824a144b79b6d4f25a60ee0a9669db3">
      		<secHashInfoBase64>eyJwaGFzaCI6IjUwMTEwMDgzMDAwMjMwMTAiLCJwZHFIYXNoIjoiMWVkYjNlNjJjZGE4YzUwMGIy
      NGQzYTEyYzNhMWRhNmZkZTY3Y2IyNjRkYTJlNWFkZDBhNjRjOTJiZTUyYjQ0YiJ9
      </secHashInfoBase64>
      		<live>
      			<duration>0</duration>
      			<size>0</size>
      			<md5 />
      			<fileid />
      			<hdsize>0</hdsize>
      			<hdmd5 />
      			<hdfileid />
      			<stillimagetimems>0</stillimagetimems>
      		</live>
      	</img>
      	<platform_signature />
      	<imgdatahash />
      	<ImgSourceInfo>
      		<ImgSourceUrl />
      		<BizType>0</BizType>
      	</ImgSourceInfo>
      </msg>
      
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [33b19b60] 🤖 开始统一AI响应处理 - Length: 71, ToUser: wxid_scqpt8dyuxqv41, ToGroup: (null)
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [33b19b60] 📝 AI响应拆分完成 - 原始长度: 71, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: 3ce1ce56, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: 3ce1ce56, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [33b19b60] ✅ AI响应已入队统一发送队列 - BatchId: 3ce1ce56, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [f5a55187] ✅ AI请求处理完成 - BatchId: 3ce1ce56, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
warn: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      ⚠️ 未知的命令类型 - CommandType: <>f__AnonymousType66`5
warn: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ❌ 发送消息已标记为失败 - MessageId: ae72c1e3-54c5-4e89-89bd-f674f77fbda3, Error: 未知的命令类型
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [2744a4e0] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, MessageType: 60008
info: HappyWechat.Web.Controllers.WxController[0]
      [a9668faf] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [ffb7279c] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, MessageType: 60009
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: ffb7279c, StreamingType: MediaSlow, OriginalType: 60009
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理媒体消息 - ProcessingId: ffb7279c, MessageType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [ffb7279c] 🚀 统一消息处理器开始处理 - MessageType: 60009, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Slow, ProcessingId: ffb7279c, MessageType: 60009, StreamingType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [ffb7279c] ✅ 流式架构路由完成 - Duration: 7.8055ms, Result: 消息已路由到MediaSlow通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [ffb7279c] ✅ 回调数据处理完成 - MessageType: 60009, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [87b48f6b] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [ffb7279c] 📁 媒体处理请求已入队 - MessageId: 7d0d1bf3-8bb6-45cd-9904-47bb02e3fef3, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, MessageType: 60009
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [ffb7279c] ✅ 统一消息处理完成 - Category: PrivateMedia, Duration: 4.1485ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构媒体消息处理结果 - ProcessingId: ffb7279c, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Slow-2, ProcessingId: ffb7279c, MessageType: MediaSlow, Duration: 4.3834ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [ffb7279c] 📁 开始媒体消息AI处理 - MessageType: 60009, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.EYun.EYunDownloadWrapper[0]
      EYun文件下载API调用 - Endpoint: /getMsgFile, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: **********
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      通过MemoryStream获取流大小: 447 bytes - wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/file_**********_20250804170748.txt
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      MinIO文件上传成功 - Path: wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/file_**********_20250804170748.txt, Size: 447 bytes
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      文件下载并存储成功 - URL: http://**************:35684/wechat/wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/file_**********_20250804170748.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=lRH2411C25mhOJ4X8Qy4%2F20250804%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250804T090748Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=35727c3da98f6f100993bba3db42e08a001435ce2a97a378b141ce909fcb77aa, Size: 447 bytes - file_**********_20250804170748.txt
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [ffb7279c] ✅ 媒体处理完成 - URL: http://**************:35684/wechat/wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/file_**********_20250804170748.txt
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [ffb7279c] 📝 AI请求已入队 - MessageId: da3f503c-6c5f-46a2-a971-f04744df85ed, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 3
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [ffb7279c] ✅ 媒体消息AI处理完成 - Duration: 722.2975ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [ffb7279c] ✅ 媒体请求处理完成 - AiProcessed: True
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      📱 账号媒体消息处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [ChatGPT] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_scqpt8dyuxqv41，文件消息，媒体文件链接：http://**************:35684/wechat/wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/file_**********_20250804170748.txt，附加内容：<?xml version="1.0"?>
      <msg>
      	<appmsg appid="" sdkver="0">
      		<title>deppseach.txt</title>
      		<des />
      		<username />
      		<action>view</action>
      		<type>6</type>
      		<showtype>0</showtype>
      		<content />
      		<url />
      		<lowurl />
      		<forwardflag>0</forwardflag>
      		<dataurl />
      		<lowdataurl />
      		<contentattr>0</contentattr>
      		<streamvideo>
      			<streamvideourl />
      			<streamvideototaltime>0</streamvideototaltime>
      			<streamvideotitle />
      			<streamvideowording />
      			<streamvideoweburl />
      			<streamvideothumburl />
      			<streamvideoaduxinfo />
      			<streamvideopublishid />
      		</streamvideo>
      		<canvasPageItem>
      			<canvasPageXml><![CDATA[]]></canvasPageXml>
      		</canvasPageItem>
      		<appattach>
      			<attachid>@cdn_3057020100044b30490201000204114898ab02032f533f0204a380336f020468907862042465393065303863372d646361662d343463372d383731302d3466313264326437303033650204051400050201000405004c550700_91301cde04d9a68432015aba352d398d_1</attachid>
      			<cdnattachurl>3057020100044b30490201000204114898ab02032f533f0204a380336f020468907862042465393065303863372d646361662d343463372d383731302d3466313264326437303033650204051400050201000405004c550700</cdnattachurl>
      			<totallen>447</totallen>
      			<aeskey>91301cde04d9a68432015aba352d398d</aeskey>
      			<encryver>1</encryver>
      			<fileext>txt</fileext>
      			<islargefilemsg>0</islargefilemsg>
      			<overwrite_newmsgid>1181100313213383085</overwrite_newmsgid>
      			<fileuploadtoken><![CDATA[v1_cKxbu8W9g/USba0KiMtTVkudCxfzm96+5AQ/p9Ai5+o76rSGrDdrCZ9alVzZeBZ/3L0ZGVhKMF4vBNZ9gjKAo4wJdhbZ/Vj1aIgxznQGLQb7wIaTi4RPwzae50C8cVQpxH4TINZInakYaZRDcQYpvSqU0gLtb1mGn/ougE6s0hGH+Mbk5M71f0n4X3izx8k=]]></fileuploadtoken>
      		</appattach>
      		<extinfo />
      		<androidsource>0</androidsource>
      		<thumburl />
      		<mediatagname />
      		<messageaction><![CDATA[]]></messageaction>
      		<messageext><![CDATA[]]></messageext>
      		<emoticongift>
      			<packageflag>0</packageflag>
      			<packageid />
      		</emoticongift>
      		<emoticonshared>
      			<packageflag>0</packageflag>
      			<packageid />
      		</emoticonshared>
      		<designershared>
      			<designeruin>0</designeruin>
      			<designername>null</designername>
      			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
      		</designershared>
      		<emotionpageshared>
      			<tid>0</tid>
      			<title>null</title>
      			<desc>null</desc>
      			<iconUrl><![CDATA[null]]></iconUrl>
      			<secondUrl />
      			<pageType>0</pageType>
      			<setKey>null</setKey>
      		</emotionpageshared>
      		<webviewshared>
      			<shareUrlOriginal />
      			<shareUrlOpen />
      			<jsAppId />
      			<publisherId />
      			<publisherReqId />
      		</webviewshared>
      		<template_id />
      		<md5>444c60b05cbfdad054d07884bf64b924</md5>
      		<websearch />
      		<weappinfo>
      			<username />
      			<appid />
      			<appservicetype>0</appservicetype>
      			<secflagforsinglepagemode>0</secflagforsinglepagemode>
      			<videopageinfo>
      				<thumbwidth>0</thumbwidth>
      				<thumbheight>0</thumbheight>
      				<fromopensdk>0</fromopensdk>
      			</videopageinfo>
      		</weappinfo>
      		<statextstr />
      		<musicShareItem>
      			<musicDuration>0</musicDuration>
      		</musicShareItem>
      		<finderLiveProductShare>
      			<finderLiveID><![CDATA[]]></finderLiveID>
      			<finderUsername><![CDATA[]]></finderUsername>
      			<finderObjectID><![CDATA[]]></finderObjectID>
      			<finderNonceID><![CDATA[]]></finderNonceID>
      			<liveStatus><![CDATA[]]></liveStatus>
      			<appId><![CDATA[]]></appId>
      			<pagePath><![CDATA[]]></pagePath>
      			<productId><![CDATA[]]></productId>
      			<coverUrl><![CDATA[]]></coverUrl>
      			<productTitle><![CDATA[]]></productTitle>
      			<marketPrice><![CDATA[0]]></marketPrice>
      			<sellingPrice><![CDATA[0]]></sellingPrice>
      			<platformHeadImg><![CDATA[]]></platformHeadImg>
      			<platformName><![CDATA[]]></platformName>
      			<shopWindowId><![CDATA[]]></shopWindowId>
      			<flashSalePrice><![CDATA[0]]></flashSalePrice>
      			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
      			<ecSource><![CDATA[]]></ecSource>
      			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
      			<platformIconURL><![CDATA[]]></platformIconURL>
      			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
      			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
      			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
      			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
      			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
      			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
      			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
      			<isPriceBeginShow>false</isPriceBeginShow>
      			<lastGMsgID><![CDATA[]]></lastGMsgID>
      			<promoterKey><![CDATA[]]></promoterKey>
      			<discountWording><![CDATA[]]></discountWording>
      			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
      			<productCardKey><![CDATA[]]></productCardKey>
      			<isWxShop><![CDATA[]]></isWxShop>
      			<brandIconUrl><![CDATA[]]></brandIconUrl>
      			<rIconUrl><![CDATA[]]></rIconUrl>
      			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
      			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
      			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
      			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
      			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
      			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
      			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
      			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
      			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
      			<showBoxItemStringList />
      		</finderLiveProductShare>
      		<finderOrder>
      			<appID><![CDATA[]]></appID>
      			<orderID><![CDATA[]]></orderID>
      			<path><![CDATA[]]></path>
      			<priceWording><![CDATA[]]></priceWording>
      			<stateWording><![CDATA[]]></stateWording>
      			<productImageURL><![CDATA[]]></productImageURL>
      			<products><![CDATA[]]></products>
      			<productsCount><![CDATA[0]]></productsCount>
      			<orderType><![CDATA[0]]></orderType>
      			<newPriceWording><![CDATA[]]></newPriceWording>
      			<newStateWording><![CDATA[]]></newStateWording>
      			<useNewWording><![CDATA[0]]></useNewWording>
      		</finderOrder>
      		<finderShopWindowShare>
      			<finderUsername><![CDATA[]]></finderUsername>
      			<avatar><![CDATA[]]></avatar>
      			<nickname><![CDATA[]]></nickname>
      			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
      			<appId><![CDATA[]]></appId>
      			<path><![CDATA[]]></path>
      			<appUsername><![CDATA[]]></appUsername>
      			<query><![CDATA[]]></query>
      			<liteAppId><![CDATA[]]></liteAppId>
      			<liteAppPath><![CDATA[]]></liteAppPath>
      			<liteAppQuery><![CDATA[]]></liteAppQuery>
      			<platformTagURL><![CDATA[]]></platformTagURL>
      			<saleWording><![CDATA[]]></saleWording>
      			<lastGMsgID><![CDATA[]]></lastGMsgID>
      			<profileTypeWording><![CDATA[]]></profileTypeWording>
      			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
      			<isWxShop><![CDATA[]]></isWxShop>
      			<platformIconUrl><![CDATA[]]></platformIconUrl>
      			<brandIconUrl><![CDATA[]]></brandIconUrl>
      			<description><![CDATA[]]></description>
      			<backgroundUrl><![CDATA[]]></backgroundUrl>
      			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
      			<rIconUrl><![CDATA[]]></rIconUrl>
      			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
      			<rWords><![CDATA[]]></rWords>
      			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
      			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
      			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
      			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
      			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
      			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
      			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
      			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
      			<reputationInfo>
      				<hasReputationInfo>0</hasReputationInfo>
      				<reputationScore>0</reputationScore>
      				<reputationWording />
      				<reputationTextColor />
      				<reputationLevelWording />
      				<reputationBackgroundColor />
      			</reputationInfo>
      			<productImageURLList />
      		</finderShopWindowShare>
      		<findernamecard>
      			<username />
      			<avatar><![CDATA[]]></avatar>
      			<nickname />
      			<auth_job />
      			<auth_icon>0</auth_icon>
      			<auth_icon_url />
      			<ecSource><![CDATA[]]></ecSource>
      			<lastGMsgID><![CDATA[]]></lastGMsgID>
      		</findernamecard>
      		<finderGuarantee>
      			<scene><![CDATA[0]]></scene>
      		</finderGuarantee>
      		<directshare>0</directshare>
      		<gamecenter>
      			<namecard>
      				<iconUrl />
      				<name />
      				<desc />
      				<tail />
      				<jumpUrl />
      				<liteappId />
      				<liteappPath />
      				<liteappQuery />
      				<liteappMinVersion />
      			</namecard>
      		</gamecenter>
      		<patMsg>
      			<chatUser />
      			<records>
      				<recordNum>0</recordNum>
      			</records>
      		</patMsg>
      		<secretmsg>
      			<issecretmsg>0</issecretmsg>
      		</secretmsg>
      		<referfromscene>0</referfromscene>
      		<gameshare>
      			<liteappext>
      				<liteappbizdata />
      				<priority>0</priority>
      			</liteappext>
      			<appbrandext>
      				<litegameinfo />
      				<priority>-1</priority>
      			</appbrandext>
      			<gameshareid />
      			<sharedata />
      			<isvideo>0</isvideo>
      			<duration>0</duration>
      			<isexposed>0</isexposed>
      			<readtext />
      		</gameshare>
      		<tingChatRoomItem>
      			<type>0</type>
      			<categoryItem>null</categoryItem>
      		</tingChatRoomItem>
      		<mpsharetrace>
      			<hasfinderelement>0</hasfinderelement>
      			<lastgmsgid />
      		</mpsharetrace>
      		<wxgamecard>
      			<framesetname />
      			<mbcarddata />
      			<minpkgversion />
      			<clientextinfo />
      			<mbcardheight>0</mbcardheight>
      			<isoldversion>0</isoldversion>
      		</wxgamecard>
      		<ecskfcard>
      			<framesetname />
      			<mbcarddata />
      			<minupdateunixtimestamp>0</minupdateunixtimestamp>
      			<needheader>false</needheader>
      			<summary />
      		</ecskfcard>
      		<liteapp>
      			<id>null</id>
      			<path />
      			<query />
      			<istransparent>0</istransparent>
      			<hideicon>0</hideicon>
      			<forbidforward>0</forbidforward>
      		</liteapp>
      		<opensdk_share_is_modified>0</opensdk_share_is_modified>
      	</appmsg>
      	<fromusername>wxid_scqpt8dyuxqv41</fromusername>
      	<scene>0</scene>
      	<appinfo>
      		<version>1</version>
      		<appname></appname>
      	</appinfo>
      	<commenturl></commenturl>
      </msg>
      
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [9dd8dd97] 🤖 开始统一AI响应处理 - Length: 45, ToUser: wxid_scqpt8dyuxqv41, ToGroup: (null)
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [9dd8dd97] 📝 AI响应拆分完成 - 原始长度: 45, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: 522bde68, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: 522bde68, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [9dd8dd97] ✅ AI响应已入队统一发送队列 - BatchId: 522bde68, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [ffb7279c] ✅ AI请求处理完成 - BatchId: 522bde68, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
warn: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      ⚠️ 未知的命令类型 - CommandType: <>f__AnonymousType66`5
warn: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ❌ 发送消息已标记为失败 - MessageId: dfb2f3cb-5428-4f93-b693-489f77391eb5, Error: 未知的命令类型
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [075403f5] 📥 收到EYun回调数据: FromGroup: 53451126890@chatroom, Content: @心 你好, MessageType: 80001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: 075403f5, StreamingType: PriorityAt, OriginalType: 80001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Priority, ProcessingId: 075403f5, MessageType: 80001, StreamingType: PriorityAt
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [075403f5] ✅ 流式架构路由完成 - Duration: 11.8777ms, Result: 消息已路由到PriorityAtMessage通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [075403f5] ✅ 回调数据处理完成 - MessageType: 80001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [396f7a75] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理优先级消息 - ProcessingId: 075403f5, MessageType: PriorityAt
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [075403f5] 🚀 统一消息处理器开始处理 - MessageType: 80001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 检查群消息AI回复触发条件 - MessageType: 80001, Group: 53451126890@chatroom, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      📋 群组配置检查 - GroupName: 花开了, IsAiEnabled: True, AiAgentId: a657e9fa-809b-4a7a-9c76-74dec4277ba7
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 开始@检测 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], Content: '@心 你好'
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ✅ @检测成功 - 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 @状态检查完成 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], IsMentioned: True, Method: 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      📝 应用@提及规则 - OnlyReplyWhenMentioned: True, IsMentioned: True
info: HappyWechat.Infrastructure.MessageProcessing.Services.MessageCombinationService[0]
      [23cfbfe7] 🔄 消息组合处理 - MessageType: 80001, FromGroup: 53451126890@chatroom
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ⚡ 群组@模式 - 消息立即处理：单独文本消息 - ProcessingId: 23cfbfe7
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 开始@检测 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], Content: '@心 你好'
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ✅ @检测成功 - 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 @状态检查完成 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], IsMentioned: True, Method: 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 群消息处理结果 - MessageType: 80001, OnlyReplyWhenMentioned: True, IsMentioned: True, ShouldReply: True, HasCombinedMessage: False
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [075403f5] 📝 AI请求已入队 - MessageId: 509025df-6681-4f6f-97f8-9682a09ef6fb, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 10
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [075403f5] ✅ 统一消息处理完成 - Category: GroupText, Duration: 66.4274ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ⚡ 流式架构优先级消息处理完成 - ProcessingId: 075403f5, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Priority-0, ProcessingId: 075403f5, MessageType: PriorityAt, Duration: 68.414ms
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [ChatGPT] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_scqpt8dyuxqv41,FromGroup: 53451126890@chatroom,FromGroupUser: wxid_scqpt8dyuxqv41,SenderNickname: 张涛，群聊文本消息内容：@心 你好
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [602c3e40] 🤖 开始统一AI响应处理 - Length: 42, ToUser: wxid_scqpt8dyuxqv41, ToGroup: 53451126890@chatroom
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [602c3e40] 📝 AI响应拆分完成 - 原始长度: 42, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: 1947b051, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: 1947b051, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [602c3e40] ✅ AI响应已入队统一发送队列 - BatchId: 1947b051, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [075403f5] ✅ AI请求处理完成 - BatchId: 1947b051, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
warn: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      ⚠️ 未知的命令类型 - CommandType: <>f__AnonymousType66`5
warn: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ❌ 发送消息已标记为失败 - MessageId: 326ca1f6-d41e-4e41-bff8-533fc56ca710, Error: 未知的命令类型
