[ENTRYPOINT] HappyWechat 容器启动脚本开始执行
[ENTRYPOINT] 当前用户: root (UID: 0)
[ENTRYPOINT] 以root用户运行，将修复权限后切换到应用用户
[ENTRYPOINT] 执行权限修复流程...
[ENTRYPOINT] 以root身份修复权限...
[ENTRYPOINT] 创建必要目录...
[ENTRYPOINT] 创建目录: /app/logs
[SUCCESS] 目录创建完成
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys
[ENTRYPOINT] 数据保护备份目录已创建
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys/escrow
[SUCCESS] 权限修复完成
[ENTRYPOINT] 切换到应用用户并启动应用
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建数据库连接字符串: Server=172.19.0.2;Port=3306;Database=huakai
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建Redis连接字符串: 172.19.0.3:6379
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      开始执行ID映射缓存预热...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      开始预热活跃账号缓存...
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式消息处理架构启动 - 三通道异步流水线模式
info: HappyWechat.Infrastructure.MessageQueue.MessageQueueMonitorService[0]
      🔍 消息队列监控服务启动
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Priority 通道处理器 - 并发度: 4
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Fast 通道处理器 - 并发度: 8
info: HappyWechat.Infrastructure.MessageProcessing.Monitoring.PipelineMonitor[0]
      🔍 流水线监控服务启动
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 启动 Slow 通道处理器 - 并发度: 16
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      🚀 统一EYun发送队列消费者启动
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🚀 统一消息消费者启动 - 账号级别隔离并行处理模式，负责处理AI请求和媒体请求队列
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      缓存预热完成 - 账号数: 1, 耗时: 1446ms
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      ID映射缓存预热完成 - 账号数: 1, 耗时: 1814ms
info: HappyWechat.Infrastructure.ServiceRegistration.ServiceHealthValidator[0]
      ✅ 启动验证成功完成，耗时: 1.8522ms
warn: Microsoft.AspNetCore.Hosting.Diagnostics[15]
      Overriding HTTP_PORTS '8080' and HTTPS_PORTS ''. Binding to values defined by URLS instead 'http://+:5215'.
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [95931a36] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, Content: 你好, MessageType: 60001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Fast, ProcessingId: 95931a36, MessageType: 60001, StreamingType: TextFast
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: 95931a36, StreamingType: TextFast, OriginalType: 60001
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [95931a36] ✅ 流式架构路由完成 - Duration: 55.9898ms, Result: 消息已路由到PrivateTextFast通道
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理文本消息 - ProcessingId: 95931a36, MessageType: TextFast
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [95931a36] ✅ 回调数据处理完成 - MessageType: 60001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [95931a36] 🚀 统一消息处理器开始处理 - MessageType: 60001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [c907a3e2] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [95931a36] 📝 AI请求已入队 - MessageId: 904644ed-946a-4a99-b9eb-38d4d517d2e2, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 8
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [95931a36] ✅ 统一消息处理完成 - Category: PrivateText, Duration: 58.8652ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构文本消息处理结果 - ProcessingId: 95931a36, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Fast-0, ProcessingId: 95931a36, MessageType: TextFast, Duration: 62.2744ms
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [MaxKB] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_scqpt8dyuxqv41，文本消息内容：你好
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [046ef7d1] 🤖 开始统一AI响应处理 - Length: 17, ToUser: wxid_scqpt8dyuxqv41, ToGroup: (null)
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 开始Markdown处理 - 原始长度: 17
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 Markdown处理完成 - 处理后长度: 17
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [046ef7d1] 📝 Markdown过滤完成 - 原长度: 17, 过滤后: 17
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [046ef7d1] 🔍 内容解析完成 - 拆分为: 1个片段
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [046ef7d1] 📝 AI响应拆分完成 - 原始长度: 17, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: 49222ea9, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: 49222ea9, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [046ef7d1] ✅ AI响应已入队统一发送队列 - BatchId: 49222ea9, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [95931a36] ✅ AI请求处理完成 - BatchId: 49222ea9, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      📦 获取待发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1, CurrentTime: 1754304896467
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      📦 处理发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      📤 消息已发送 - MessageId: 2c7ff123-271f-4299-bf8c-e0f762f3117d, CommandType: WxSendTextMessageCommand
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [01962385] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, MessageType: 60002
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Slow, ProcessingId: 01962385, MessageType: 60002, StreamingType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [01962385] ✅ 流式架构路由完成 - Duration: 7.3881ms, Result: 消息已路由到MediaSlow通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [01962385] ✅ 回调数据处理完成 - MessageType: 60002, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: 01962385, StreamingType: MediaSlow, OriginalType: 60002
info: HappyWechat.Web.Controllers.WxController[0]
      [990234d1] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理媒体消息 - ProcessingId: 01962385, MessageType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [01962385] 🚀 统一消息处理器开始处理 - MessageType: 60002, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [01962385] 📁 媒体处理请求已入队 - MessageId: ab8a581c-dfc9-48a8-9fbe-0027f5921ebe, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, MessageType: 60002
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [01962385] ✅ 统一消息处理完成 - Category: PrivateMedia, Duration: 7.5334ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构媒体消息处理结果 - ProcessingId: 01962385, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Slow-0, ProcessingId: 01962385, MessageType: MediaSlow, Duration: 10.064ms
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [4fa3b096] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, MessageType: 60004
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: 4fa3b096, StreamingType: MediaSlow, OriginalType: 60004
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理媒体消息 - ProcessingId: 4fa3b096, MessageType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [4fa3b096] 🚀 统一消息处理器开始处理 - MessageType: 60004, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Slow, ProcessingId: 4fa3b096, MessageType: 60004, StreamingType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [4fa3b096] ✅ 流式架构路由完成 - Duration: 8.7732ms, Result: 消息已路由到MediaSlow通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [4fa3b096] ✅ 回调数据处理完成 - MessageType: 60004, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [0aa74e2a] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [4fa3b096] 📁 媒体处理请求已入队 - MessageId: 6a2f32bf-e097-4f86-b933-3db8bdc03e2e, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, MessageType: 60004
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [4fa3b096] ✅ 统一消息处理完成 - Category: PrivateMedia, Duration: 6.2427ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构媒体消息处理结果 - ProcessingId: 4fa3b096, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Slow-1, ProcessingId: 4fa3b096, MessageType: MediaSlow, Duration: 7.4428ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [4fa3b096] 📁 开始媒体消息AI处理 - MessageType: 60004, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [6004270e] 🎵 开始处理语音消息 - MsgId: 276801775, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c
warn: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [6004270e] 原始回调数据中的语音参数无效 - Length: 0, BufId: (null)
warn: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [6004270e] 回调数据参数提取失败，尝试XML解析
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      🔧 开始解析语音消息XML - Content: <msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1743" length="2797" bufid="0" aeskey="c12506af798c792df088334bba3c1b36" voiceurl="3052020100044b30490201000204857b835a02032f533f0204f380336f0204689091b2042464623564316133302d663836332d343765322d386564312d663564633
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      ✅ 语音XML解析成功 - Length: 2797, BufId: 0, VoiceLength: 1743
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      ✅ 语音消息XML解析成功 - Length: 2797, BufId: 0, Source: XML解析-voicemsg标签
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [6004270e] ✅ 语音参数验证通过 - Length: 2797, BufId: 0
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [6004270e] 📋 语音参数提取成功 - Length: 2797, BufId: 0, Source: XML解析-voicemsg标签
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [6004270e] 🔧 构建EYun语音下载请求 - WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: 276801775, Length: 2797, BufId: 0, FromUser: wxid_scqpt8dyuxqv41
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [6004270e] 📡 调用EYun语音下载API - Length: 2797, BufId: 0
info: HappyWechat.Infrastructure.EYun.EYunDownloadWrapper[0]
      EYun语音下载API调用 - Endpoint: /getMsgVoice, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: 276801775, Length: 2797, BufId: 0
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [6004270e] ✅ EYun语音下载API调用成功
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [6004270e] ✅ EYun语音下载链接获取成功 - URL: http://wxapii.oos-hazz.ctyunapi.cn/20250804/wxid_ic3nmv9anggz22/3e52aa69-027b-4093-b7fe-7308dc5f76f8.silk?AWSAccessKeyId=9e882e7187c38b431303&Expires=**********&Signature=iEb8jSfDr5L8mRzv2u2h5F8wda0%3D
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      MinIO文件上传成功 - Path: wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/voice_276801775_20250804185547.mp3, Size: 24076 bytes
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      [6004270e] ✅ 语音处理完成 - 文件: voice_276801775_20250804185547.mp3, URL: http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/voice_276801775_20250804185547.mp3, 大小: 24076字节
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [4fa3b096] ✅ 媒体处理完成 - URL: http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/voice_276801775_20250804185547.mp3
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [4fa3b096] 📝 AI请求已入队 - MessageId: a031c6c0-0074-453d-aaf0-0266e61bff8a, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 7
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [4fa3b096] ✅ 媒体消息AI处理完成 - Duration: 1635.8333ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [4fa3b096] ✅ 媒体请求处理完成 - AiProcessed: True
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      📱 账号媒体消息处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [MaxKB] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_scqpt8dyuxqv41，语音消息，媒体文件链接：http://**************:35684/wechat/wx-voices/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/voice_276801775_20250804185547.mp3
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [74ed5b2e] 🤖 开始统一AI响应处理 - Length: 42, ToUser: wxid_scqpt8dyuxqv41, ToGroup: (null)
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 开始Markdown处理 - 原始长度: 42
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 Markdown处理完成 - 处理后长度: 42
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [74ed5b2e] 📝 Markdown过滤完成 - 原长度: 42, 过滤后: 42
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [74ed5b2e] 🔍 内容解析完成 - 拆分为: 1个片段
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [74ed5b2e] 📝 AI响应拆分完成 - 原始长度: 42, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: 5c127705, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: 5c127705, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [74ed5b2e] ✅ AI响应已入队统一发送队列 - BatchId: 5c127705, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [4fa3b096] ✅ AI请求处理完成 - BatchId: 5c127705, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      📦 获取待发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1, CurrentTime: 1754304950717
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      📦 处理发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      📤 消息已发送 - MessageId: 35217aa4-090b-498e-ab85-db25b1c2f5d7, CommandType: WxSendTextMessageCommand
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [ce4784fe] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, MessageType: 60008
warn: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      ⚠️ 忽略废弃的消息类型 - MessageType: 60008, 请使用60009/80009替代
info: HappyWechat.Web.Controllers.WxController[0]
      [6b04ab48] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [0af8600a] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, MessageType: 60009
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: 0af8600a, StreamingType: MediaSlow, OriginalType: 60009
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理媒体消息 - ProcessingId: 0af8600a, MessageType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [0af8600a] 🚀 统一消息处理器开始处理 - MessageType: 60009, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Slow, ProcessingId: 0af8600a, MessageType: 60009, StreamingType: MediaSlow
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [0af8600a] ✅ 流式架构路由完成 - Duration: 11.7686ms, Result: 消息已路由到MediaSlow通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [0af8600a] ✅ 回调数据处理完成 - MessageType: 60009, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [46e69806] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [0af8600a] 📁 媒体处理请求已入队 - MessageId: d04e82ee-57db-4734-a70e-4e734b27deb0, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, MessageType: 60009
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [0af8600a] ✅ 统一消息处理完成 - Category: PrivateMedia, Duration: 5.4316ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构媒体消息处理结果 - ProcessingId: 0af8600a, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Slow-2, ProcessingId: 0af8600a, MessageType: MediaSlow, Duration: 5.5751ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [0af8600a] 📁 开始媒体消息AI处理 - MessageType: 60009, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.EYun.EYunDownloadWrapper[0]
      EYun文件下载API调用 - Endpoint: /getMsgFile, WId: a0c2bdcf-8f89-4bd7-8e37-4c0736066a2c, MsgId: **********
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      通过MemoryStream获取流大小: 447 bytes - wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/file_**********_20250804185559.txt
info: HappyWechat.Infrastructure.FileStorage.MinIOFileStorageProvider[0]
      MinIO文件上传成功 - Path: wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/file_**********_20250804185559.txt, Size: 447 bytes
info: HappyWechat.Infrastructure.MediaProcessing.UnifiedMediaProcessor[0]
      文件下载并存储成功 - URL: http://**************:35684/wechat/wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/file_**********_20250804185559.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=lRH2411C25mhOJ4X8Qy4%2F20250804%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250804T105559Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=456d1ea9e841512d1f139955b187b5f59ce92d62fc0050944536c4dd3699ef53, Size: 447 bytes - file_**********_20250804185559.txt
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [0af8600a] ✅ 媒体处理完成 - URL: http://**************:35684/wechat/wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/file_**********_20250804185559.txt
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [0af8600a] 📝 AI请求已入队 - MessageId: f2e20d8a-108f-4dc0-967e-b30786dbb24c, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 3
info: HappyWechat.Infrastructure.MessageProcessing.Unified.MediaToAiProcessor[0]
      [0af8600a] ✅ 媒体消息AI处理完成 - Duration: 744.9448ms
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [0af8600a] ✅ 媒体请求处理完成 - AiProcessed: True
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      📱 账号媒体消息处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [MaxKB] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_scqpt8dyuxqv41，文件消息，媒体文件链接：http://**************:35684/wechat/wx-files/980a47bc-1452-446c-8e78-72f7bb0c5144/2025/08/04/file_**********_20250804185559.txt
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [33b4ff47] 🤖 开始统一AI响应处理 - Length: 57, ToUser: wxid_scqpt8dyuxqv41, ToGroup: (null)
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 开始Markdown处理 - 原始长度: 57
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 Markdown处理完成 - 处理后长度: 57
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [33b4ff47] 📝 Markdown过滤完成 - 原长度: 57, 过滤后: 57
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [33b4ff47] 🔍 内容解析完成 - 拆分为: 1个片段
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [33b4ff47] 📝 AI响应拆分完成 - 原始长度: 57, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: f1dabf7e, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: f1dabf7e, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [33b4ff47] ✅ AI响应已入队统一发送队列 - BatchId: f1dabf7e, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [0af8600a] ✅ AI请求处理完成 - BatchId: f1dabf7e, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      📦 获取待发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1, CurrentTime: 1754304961470
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      📦 处理发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      📤 消息已发送 - MessageId: ffaaa8f4-418f-4e60-9c3a-f2e445cd38fd, CommandType: WxSendTextMessageCommand
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [4109b134] 📥 收到EYun回调数据: FromGroup: 53451126890@chatroom, Content: @心 你好, MessageType: 80001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Priority, ProcessingId: 4109b134, MessageType: 80001, StreamingType: PriorityAt
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: 4109b134, StreamingType: PriorityAt, OriginalType: 80001
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [4109b134] ✅ 流式架构路由完成 - Duration: 10.2361ms, Result: 消息已路由到PriorityAtMessage通道
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理优先级消息 - ProcessingId: 4109b134, MessageType: PriorityAt
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [4109b134] 🚀 统一消息处理器开始处理 - MessageType: 80001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [4109b134] ✅ 回调数据处理完成 - MessageType: 80001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [67792112] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 检查群消息AI回复触发条件 - MessageType: 80001, Group: 53451126890@chatroom, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      📋 群组配置检查 - GroupName: 花开了, IsAiEnabled: True, AiAgentId: a657e9fa-809b-4a7a-9c76-74dec4277ba7
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 开始@检测 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], Content: '@心 你好'
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ✅ @检测成功 - 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 @状态检查完成 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], IsMentioned: True, Method: 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      📝 应用@提及规则 - OnlyReplyWhenMentioned: True, IsMentioned: True
info: HappyWechat.Infrastructure.MessageProcessing.Services.MessageCombinationService[0]
      [200503db] 🔄 消息组合处理 - MessageType: 80001, FromGroup: 53451126890@chatroom
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ⚡ 群组@模式 - 消息立即处理：单独文本消息 - ProcessingId: 200503db
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🔍 开始@检测 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], Content: '@心 你好'
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      ✅ @检测成功 - 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 @状态检查完成 - BotWcId: wxid_ic3nmv9anggz22, AtList: [wxid_ic3nmv9anggz22], IsMentioned: True, Method: 方式1：@列表精确匹配
info: HappyWechat.Infrastructure.MessageProcessing.Services.EnhancedGroupMessageProcessor[0]
      🎯 群消息处理结果 - MessageType: 80001, OnlyReplyWhenMentioned: True, IsMentioned: True, ShouldReply: True, HasCombinedMessage: False
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [4109b134] 📝 AI请求已入队 - MessageId: d352e944-9544-4810-a62c-0b5055ea6592, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 10
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [4109b134] ✅ 统一消息处理完成 - Category: GroupText, Duration: 50.6614ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ⚡ 流式架构优先级消息处理完成 - ProcessingId: 4109b134, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Priority-0, ProcessingId: 4109b134, MessageType: PriorityAt, Duration: 52.3744ms
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [MaxKB] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_scqpt8dyuxqv41,FromGroup: 53451126890@chatroom,FromGroupUser: wxid_scqpt8dyuxqv41,SenderNickname: 张涛，群聊文本消息内容：@心 你好
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [76b6226c] 🤖 开始统一AI响应处理 - Length: 21, ToUser: wxid_scqpt8dyuxqv41, ToGroup: 53451126890@chatroom
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 开始Markdown处理 - 原始长度: 21
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 Markdown处理完成 - 处理后长度: 21
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [76b6226c] 📝 Markdown过滤完成 - 原长度: 21, 过滤后: 21
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [76b6226c] 🔍 内容解析完成 - 拆分为: 1个片段
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [76b6226c] 📝 AI响应拆分完成 - 原始长度: 21, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: a4e537d1, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: a4e537d1, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [76b6226c] ✅ AI响应已入队统一发送队列 - BatchId: a4e537d1, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [4109b134] ✅ AI请求处理完成 - BatchId: a4e537d1, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      📦 获取待发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1, CurrentTime: 1754304981532
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      📦 处理发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      📤 消息已发送 - MessageId: 8ce5acdf-893a-4cf1-984a-a5346d687a32, CommandType: WxSendTextMessageCommand
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      ✅ SessionId获取成功 - 策略: HTTP上下文策略, SessionId: 9ce43737..., 耗时: 1ms
info: HappyWechat.Infrastructure.Auth.SessionIdRetrievalService[0]
      🔧 SessionId获取服务已初始化，策略数量: 4
info: HappyWechat.Web.Services.ContactAiConfigCacheService[0]
      联系人AI配置缓存失效完成 - ContactId: 8eff361d-c6ff-476c-be53-d8a560a2ef10
info: HappyWechat.Web.Services.ContactAiConfigCacheService[0]
      所有联系人AI配置缓存失效完成
info: HappyWechat.Web.Controllers.WxController[0]
      ✅ 联系人AI配置缓存已清除 - ContactId: 8eff361d-c6ff-476c-be53-d8a560a2ef10, ManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      ✅ 联系人AI配置变更通知已记录 - ContactId: 8eff361d-c6ff-476c-be53-d8a560a2ef10
info: HappyWechat.Infrastructure.Integration.UnifiedArchitectureIntegrationService[0]
      Clearing account cache for 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Components.Common.BaseWxPageComponent[0]
      ✅ AI配置更新成功 - Contact: 8eff361d-c6ff-476c-be53-d8a560a2ef10
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [01453b24] 📥 收到EYun回调数据: FromUser: wxid_scqpt8dyuxqv41, Content: 你好, MessageType: 60001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🔄 流式架构开始分派消息 - ProcessingId: 01453b24, StreamingType: TextFast, OriginalType: 60001
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      🚀 流式架构开始处理文本消息 - ProcessingId: 01453b24, MessageType: TextFast
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [01453b24] 🚀 统一消息处理器开始处理 - MessageType: 60001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📥 流式架构消息入队 - Channel: Fast, ProcessingId: 01453b24, MessageType: 60001, StreamingType: TextFast
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [01453b24] ✅ 流式架构路由完成 - Duration: 7.8631ms, Result: 消息已路由到PrivateTextFast通道
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [01453b24] ✅ 回调数据处理完成 - MessageType: 60001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [82f0db46] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageQueue[0]
      [01453b24] 📝 AI请求已入队 - MessageId: a03b0b09-f836-40d9-8c23-4b8861b96647, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Priority: 8
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedMessageProcessor[0]
      [01453b24] ✅ 统一消息处理完成 - Category: PrivateText, Duration: 9.4448ms
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      📋 流式架构文本消息处理结果 - ProcessingId: 01453b24, Success: True
info: HappyWechat.Infrastructure.MessageQueue.Streaming.StreamingMessageArchitecture[0]
      ✅ 流式架构处理完成 - Processor: Fast-1, ProcessingId: 01453b24, MessageType: TextFast, Duration: 9.59ms
info: HappyWechat.Infrastructure.AiAgent.AiRequestLogger[0]
      🚀 [CoZe] AI请求开始，请求内容：WId: ,WcId: wxid_ic3nmv9anggz22,FromUser: wxid_scqpt8dyuxqv41，文本消息内容：你好
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [d3c9265c] 🤖 开始统一AI响应处理 - Length: 70, ToUser: wxid_scqpt8dyuxqv41, ToGroup: (null)
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 开始Markdown处理 - 原始长度: 70
info: HappyWechat.Infrastructure.Services.MarkdownProcessor[0]
      📝 Markdown处理完成 - 处理后长度: 69
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [d3c9265c] 📝 Markdown过滤完成 - 原长度: 70, 过滤后: 69
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [d3c9265c] 🔍 内容解析完成 - 拆分为: 1个片段
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [d3c9265c] 📝 AI响应拆分完成 - 原始长度: 70, 拆分为: 1条消息
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      🔄 开始批量入队发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, BatchId: ba048e70, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      ✅ 批量入队完成 - BatchId: ba048e70, 首条延迟: 0ms
info: HappyWechat.Infrastructure.MessageProcessing.Unified.UnifiedAiResponseProcessor[0]
      [d3c9265c] ✅ AI响应已入队统一发送队列 - BatchId: ba048e70, 消息数: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      [01453b24] ✅ AI请求处理完成 - BatchId: ba048e70, MessageCount: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedMessageConsumer[0]
      🤖 账号AI请求处理完成 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, 处理数量: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueue[0]
      📦 获取待发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1, CurrentTime: 1754305019574
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      📦 处理发送消息 - WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144, Count: 1
info: HappyWechat.Infrastructure.MessageQueue.Unified.UnifiedEYunSendQueueConsumer[0]
      📤 消息已发送 - MessageId: 8ab1e798-370f-4c44-b5f6-78da4268da85, CommandType: WxSendTextMessageCommand
