{"permissions": {"allow": ["Bash(find:*)", "Bash(rg:*)", "Bash(grep:*)", "Bash(dotnet build:*)", "Bash(dotnet clean:*)", "Bash(dotnet restore:*)", "Bash(dotnet run:*)", "Bash(redis-cli:*)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "Bash(/dev/null)", "Bash(ls:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(sed:*)", "WebFetch(domain:stackoverflow.com)", "WebFetch(domain:learn.microsoft.com)", "WebFetch(domain:github.com)", "Bash(grep -n -A 20 -B 5 \"Post.*callback\\|回调\\|CallbackMessage\" /mnt/c/Users/<USER>/Desktop/HappyWechat/src/HappyWechat.Web/Controllers/WxController.cs)"], "deny": []}}