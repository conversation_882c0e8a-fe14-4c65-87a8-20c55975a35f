using Microsoft.Extensions.Logging;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.DTOs.Wrappers.EYun.Requests.Downloads;
using HappyWechat.Application.DTOs.Wrappers.EYun.Responses.Downloads;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.MediaProcessing.Models;
using HappyWechat.Infrastructure.Audio;
using HappyWechat.Infrastructure.Audio.Models;
using HappyWechat.Infrastructure.Services;
using HappyWechat.Infrastructure.Caching;
using Microsoft.EntityFrameworkCore;
using HappyWechat.Infrastructure.Identity.Repositories;
using System.Diagnostics;
using System.IO;
using System.Xml.Linq;
using System.Xml;

namespace HappyWechat.Infrastructure.MediaProcessing;

/// <summary>
/// 统一媒体处理器实现
/// 负责处理所有微信媒体消息的下载、存储和AI传递
/// </summary>
public class UnifiedMediaProcessor : IUnifiedMediaProcessor
{
    private readonly IEYunDownloadWrapper _eYunDownloadWrapper;
    private readonly IFileUploadService _fileUploadService;
    private readonly ISilkAudioConverter _silkAudioConverter;
    private readonly IUrlCleaningService _urlCleaningService;
    private readonly IMediaFailureNotificationService _mediaFailureNotificationService;
    private readonly IWxManagerIdCacheService _wxManagerIdCacheService;
    private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
    // 已移除旧架构的文件编排器
    private readonly ILogger<UnifiedMediaProcessor> _logger;
    
    // 🔧 重构：支持的消息类型 - 使用60009/80009替代60008/80008
    private static readonly Dictionary<string, MediaTypeInfo> SupportedMessageTypes = new()
    {
        { "60002", new MediaTypeInfo("image", "getMsgImg", "jpg", false) }, // 私聊图片
        { "80002", new MediaTypeInfo("image", "getMsgImg", "jpg", false) }, // 群聊图片
        { "60004", new MediaTypeInfo("voice", "getMsgVoice", "mp3", true) }, // 私聊语音
        { "80004", new MediaTypeInfo("voice", "getMsgVoice", "mp3", true) }, // 群聊语音
        { "60009", new MediaTypeInfo("file", "getMsgFile", "", false) },     // 私聊文件发送完成消息
        { "80009", new MediaTypeInfo("file", "getMsgFile", "", false) }      // 群聊文件发送完成消息
    };

    public UnifiedMediaProcessor(
        IEYunDownloadWrapper eYunDownloadWrapper,
        IFileUploadService fileUploadService,
        ISilkAudioConverter silkAudioConverter,
        IUrlCleaningService urlCleaningService,
        IMediaFailureNotificationService mediaFailureNotificationService,
        IWxManagerIdCacheService wxManagerIdCacheService,
        IDbContextFactory<ApplicationDbContext> dbContextFactory,
        // 已移除旧架构的文件编排器参数
        ILogger<UnifiedMediaProcessor> logger)
    {
        _eYunDownloadWrapper = eYunDownloadWrapper;
        _fileUploadService = fileUploadService;
        _silkAudioConverter = silkAudioConverter;
        _urlCleaningService = urlCleaningService;
        _mediaFailureNotificationService = mediaFailureNotificationService;
        _wxManagerIdCacheService = wxManagerIdCacheService;
        _dbContextFactory = dbContextFactory;
        // 已移除旧架构的文件编排器初始化
        _logger = logger;
    }

    public async Task<MediaProcessingResult> ProcessMediaAsync(WxCallbackMessageDto callbackMessage)
    {
        return await ProcessMediaMessageAsync(callbackMessage, CancellationToken.None);
    }

    public async Task<MediaProcessingResult> ProcessMediaMessageAsync(WxCallbackMessageDto callbackMessage, CancellationToken cancellationToken = default)
    {
        var processingId = Guid.NewGuid().ToString("N")[..8];
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // 🔧 注释冗余的媒体处理开始日志 - 已合并到统一消息接收日志中
            // _logger.LogInformation("[{ProcessingId}] 开始处理媒体消息 - MessageType: {MessageType}, WcId: {WcId}",
            //     processingId, callbackMessage.MessageType, callbackMessage.WcId);

            // 1. 验证消息类型
            if (!IsMediaMessageType(callbackMessage.MessageType))
            {
                return MediaProcessingResult.CreateFailure($"不支持的消息类型: {callbackMessage.MessageType}", callbackMessage.MessageType);
            }

            // 2. 验证消息数据
            if (callbackMessage.Data == null || callbackMessage.Data.MsgId == 0)
            {
                return MediaProcessingResult.CreateFailure("消息数据不完整", callbackMessage.MessageType);
            }

            // 3. 创建下载请求
            var downloadRequest = MediaDownloadRequest.FromCallbackMessage(callbackMessage, processingId);

            // 🔧 修复：提取真正的XML内容（如果Content被JSON包装）
            downloadRequest.Content = ExtractOriginalXmlContent(downloadRequest.Content, processingId);

            // 🔧 修复：获取正确的EYun WId参数
            var correctWId = await GetCorrectWIdAsync(downloadRequest.WxManagerId, downloadRequest.WId, processingId);
            if (string.IsNullOrEmpty(correctWId))
            {
                return MediaProcessingResult.CreateFailure("无法获取有效的EYun WId参数", callbackMessage.MessageType);
            }
            downloadRequest.WId = correctWId;

            // 🔧 重构：根据消息类型执行相应的下载处理 - 使用60009/80009替代60008/80008
            var result = callbackMessage.MessageType switch
            {
                "60002" or "80002" => await ProcessImageMessageAsync(downloadRequest, cancellationToken),
                "60004" or "80004" => await ProcessVoiceMessageAsync(downloadRequest, cancellationToken),
                "60009" or "80009" => await ProcessFileMessageAsync(downloadRequest, cancellationToken), // 文件发送完成消息
                _ => MediaProcessingResult.CreateFailure($"未实现的消息类型处理: {callbackMessage.MessageType}", callbackMessage.MessageType)
            };

            stopwatch.Stop();
            result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            if (result.Success)
            {
                // 🎯 新架构：使用文件+@组合编排器缓存群组文件消息
                if (IsGroupFileMessage(callbackMessage.MessageType))
                {
                    try
                    {
                        // TODO: 群组文件缓存功能 - 等待SimplifiedMessageQueue架构完善后实现
                        await Task.CompletedTask;
                        _logger.LogDebug("[{ProcessingId}] 📁 群组文件消息已缓存 - MessageType: {MessageType}",
                            processingId, callbackMessage.MessageType);
                    }
                    catch (Exception cacheEx)
                    {
                        _logger.LogWarning(cacheEx, "[{ProcessingId}] ⚠️ 文件缓存失败，但不影响主流程 - MessageType: {MessageType}",
                            processingId, callbackMessage.MessageType);
                    }
                }

                // 🔧 注释冗余的媒体处理成功日志 - 媒体内容已在AI请求日志中体现
                // _logger.LogInformation("[{ProcessingId}] ✅ 媒体消息处理成功 - URL: {PublicUrl}, 耗时: {ElapsedMs}ms",
                //     processingId, result.PublicUrl, result.ProcessingTimeMs);
            }
            else
            {
                _logger.LogError("[{ProcessingId}] ❌ 媒体消息处理失败 - Error: {Error}, 耗时: {ElapsedMs}ms", 
                    processingId, result.ErrorMessage, result.ProcessingTimeMs);
            }

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "[{ProcessingId}] 媒体消息处理异常 - MessageType: {MessageType}, 耗时: {ElapsedMs}ms", 
                processingId, callbackMessage.MessageType, stopwatch.ElapsedMilliseconds);
            
            return MediaProcessingResult.CreateFailure($"处理异常: {ex.Message}", callbackMessage.MessageType);
        }
    }

    public List<string> GetSupportedMessageTypes()
    {
        return SupportedMessageTypes.Keys.ToList();
    }

    public bool IsMediaMessageType(string messageType)
    {
        return SupportedMessageTypes.ContainsKey(messageType);
    }

    public async Task<MediaProcessingCapability> GetProcessingCapabilityAsync(string messageType)
    {
        await Task.CompletedTask; // 异步方法，但当前不需要异步操作
        
        if (!SupportedMessageTypes.TryGetValue(messageType, out var typeInfo))
        {
            return new MediaProcessingCapability
            {
                IsSupported = false,
                MediaType = "unknown"
            };
        }

        return new MediaProcessingCapability
        {
            IsSupported = true,
            MediaType = typeInfo.MediaType,
            ApiEndpoint = typeInfo.ApiEndpoint,
            RequiresFormatConversion = typeInfo.RequiresFormatConversion,
            SupportedExtensions = new List<string> { typeInfo.DefaultExtension }
        };
    }

    /// <summary>
    /// 处理图片消息
    /// </summary>
    private async Task<MediaProcessingResult> ProcessImageMessageAsync(MediaDownloadRequest request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("[{ProcessingId}] 🖼️ 开始处理图片消息 - MsgId: {MsgId}, WId: {WId}",
                request.ProcessingId, request.MsgId, request.WId);

            // 🔧 修复：验证并尝试修复XML内容格式
            if (!IsValidXmlContent(request.Content, request.ProcessingId))
            {
                _logger.LogWarning("[{ProcessingId}] 图片消息XML格式无效，尝试降级处理", request.ProcessingId);
                
                // 对于图片消息，即使XML解析失败，也可以尝试通过其他方式获取
                _logger.LogInformation("[{ProcessingId}] 图片消息将使用备用下载方案", request.ProcessingId);
                // 继续处理，不直接返回失败
            }

            // 调用EYun getMsgImg API
            var downloadRequest = new EYunGetMsgImgRequest
            {
                WId = request.WId,
                MsgId = request.MsgId,
                Content = request.Content,
                Type = 0 // 0：常规图片 1：高清图
            };

            _logger.LogDebug("[{ProcessingId}] 📡 调用EYun图片下载API - WId: {WId}, MsgId: {MsgId}",
                request.ProcessingId, request.WId, request.MsgId);

            var downloadResponse = await _eYunDownloadWrapper.GetMsgImgAsync(downloadRequest);
            if (downloadResponse?.Url == null)
            {
                _logger.LogError("[{ProcessingId}] ❌ EYun图片下载API返回空结果", request.ProcessingId);
                return MediaProcessingResult.CreateFailure("EYun API返回空结果", request.MessageType);
            }

            _logger.LogInformation("[{ProcessingId}] ✅ EYun图片下载链接获取成功 - URL: {Url}",
                request.ProcessingId, downloadResponse.Url);

            // 下载并存储图片
            var fileName = $"image_{request.MsgId}_{DateTime.Now:yyyyMMddHHmmss}.jpg";
            var storageResult = await DownloadAndStoreFileAsync(downloadResponse.Url, fileName, "images", request.WxManagerId);

            // 清理URL，移除AWS签名参数，保留文件扩展名
            var cleanedUrl = _urlCleaningService.CleanUrl(storageResult.PublicUrl);

            // 🔧 注释冗余的URL清理日志
            // _logger.LogDebug("[{ProcessingId}] 图片URL清理完成 - 原始长度: {OriginalLength}, 清理后长度: {CleanedLength}",
            //     request.ProcessingId, storageResult.PublicUrl.Length, cleanedUrl.Length);

            return MediaProcessingResult.CreateSuccess(cleanedUrl, fileName, storageResult.FileSize, "image", request.MessageType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 图片消息处理失败", request.ProcessingId);

            var friendlyMessage = _mediaFailureNotificationService.GenerateFailureMessage(
                request.MessageType, ex.Message, $"image_{request.MsgId}");

            // 🔧 增强：为图片处理失败提供降级方案
            var fallbackMessage = "抱歉，我无法查看或分析图片内容。不过，如果您能提供更多关于图片的描述或具体问题，我会很乐意帮助您！";
            
            return MediaProcessingResult.CreateFailureWithFriendlyMessage(
                $"图片处理异常: {ex.Message}", fallbackMessage, request.MessageType, $"image_{request.MsgId}");
        }
    }

    /// <summary>
    /// 处理语音消息
    /// </summary>
    private async Task<MediaProcessingResult> ProcessVoiceMessageAsync(MediaDownloadRequest request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("[{ProcessingId}] 🎵 开始处理语音消息 - MsgId: {MsgId}, WId: {WId}", 
                request.ProcessingId, request.MsgId, request.WId);

            // 🔧 修复：优先使用回调数据中的直接参数，XML解析作为备用
            var voiceInfo = ExtractVoiceParametersFromCallback(request);

            if (voiceInfo == null)
            {
                _logger.LogWarning("[{ProcessingId}] 回调数据参数提取失败，尝试XML解析", request.ProcessingId);

                // 备用方案：从XML解析
                voiceInfo = ParseVoiceMessageXml(request.Content);
                if (voiceInfo == null)
                {
                    _logger.LogWarning("[{ProcessingId}] XML解析也失败，尝试降级处理", request.ProcessingId);

                    // 最后的降级策略
                    voiceInfo = TryExtractVoiceInfoFromRawContent(request.Content, request.ProcessingId);
                    if (voiceInfo == null)
                    {
                        _logger.LogError("[{ProcessingId}] ❌ 所有语音参数提取方案都失败 - Content: {Content}",
                            request.ProcessingId, request.Content?.Substring(0, Math.Min(200, request.Content?.Length ?? 0)));
                        return MediaProcessingResult.CreateFailure("无法提取语音消息参数", request.MessageType);
                    }
                }
            }

            // 🔧 新增：验证和修复语音参数
            voiceInfo = ValidateAndFixVoiceParameters(voiceInfo, request);
            if (voiceInfo == null)
            {
                _logger.LogError("[{ProcessingId}] ❌ 语音参数验证失败", request.ProcessingId);
                return MediaProcessingResult.CreateFailure("语音参数无效", request.MessageType);
            }

            _logger.LogInformation("[{ProcessingId}] 📋 语音参数提取成功 - Length: {Length}, BufId: {BufId}, Source: {Source}",
                request.ProcessingId, voiceInfo.Length, voiceInfo.BufId, voiceInfo.Source);

            // 调用EYun getMsgVoice API
            var downloadRequest = new EYunGetMsgVoiceRequest
            {
                WId = request.WId,
                MsgId = request.MsgId,
                Length = voiceInfo.Length,
                BufId = voiceInfo.BufId,
                FromUser = request.FromUser ?? string.Empty
            };

            _logger.LogInformation("[{ProcessingId}] 🔧 构建EYun语音下载请求 - WId: {WId}, MsgId: {MsgId}, Length: {Length}, BufId: {BufId}, FromUser: {FromUser}",
                request.ProcessingId, downloadRequest.WId, downloadRequest.MsgId, downloadRequest.Length, downloadRequest.BufId, downloadRequest.FromUser);

            _logger.LogInformation("[{ProcessingId}] 📡 调用EYun语音下载API - Length: {Length}, BufId: {BufId}",
                request.ProcessingId, downloadRequest.Length, downloadRequest.BufId);

            EYunGetMsgVoiceData downloadResponse;
            try
            {
                downloadResponse = await _eYunDownloadWrapper.GetMsgVoiceAsync(downloadRequest);
                if (downloadResponse?.Url == null)
                {
                    _logger.LogError("[{ProcessingId}] ❌ EYun语音下载API返回空结果", request.ProcessingId);
                    return MediaProcessingResult.CreateFailure("EYun API返回空结果", request.MessageType);
                }

                _logger.LogInformation("[{ProcessingId}] ✅ EYun语音下载API调用成功", request.ProcessingId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[{ProcessingId}] ❌ EYun语音下载API调用异常 - Error: {Error}",
                    request.ProcessingId, ex.Message);
                return MediaProcessingResult.CreateFailure($"EYun语音下载API调用失败: {ex.Message}", request.MessageType);
            }

            _logger.LogInformation("[{ProcessingId}] ✅ EYun语音下载链接获取成功 - URL: {Url}", 
                request.ProcessingId, downloadResponse.Url);

            // 下载silk文件并转换为mp3
            var fileName = $"voice_{request.MsgId}_{DateTime.Now:yyyyMMddHHmmss}.mp3";
            var storageResult = await DownloadAndConvertVoiceAsync(downloadResponse.Url, fileName, request.WxManagerId, cancellationToken);

            // 清理URL，移除AWS签名参数，保留文件扩展名
            var cleanedUrl = _urlCleaningService.CleanUrl(storageResult.PublicUrl);

            _logger.LogInformation("[{ProcessingId}] ✅ 语音处理完成 - 文件: {FileName}, URL: {CleanedUrl}, 大小: {FileSize}字节",
                request.ProcessingId, fileName, cleanedUrl, storageResult.FileSize);

            return MediaProcessingResult.CreateSuccess(cleanedUrl, fileName, storageResult.FileSize, "voice", request.MessageType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 语音消息处理失败 - MsgId: {MsgId}, 错误: {Error}", 
                request.ProcessingId, request.MsgId, ex.Message);

            var friendlyMessage = _mediaFailureNotificationService.GenerateFailureMessage(
                request.MessageType, ex.Message, $"voice_{request.MsgId}");

            // 🔧 增强：为语音处理失败提供降级方案
            var fallbackMessage = "抱歉，我无法分析这条语音消息。请您发送文字描述，我很乐意为您提供帮助！";
            
            return MediaProcessingResult.CreateFailureWithFriendlyMessage(
                $"语音处理异常: {ex.Message}", fallbackMessage, request.MessageType, $"voice_{request.MsgId}");
        }
    }

    /// <summary>
    /// 处理文件消息
    /// </summary>
    private async Task<MediaProcessingResult> ProcessFileMessageAsync(MediaDownloadRequest request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 开始下载文件 - MsgId: {MsgId}", request.ProcessingId, request.MsgId);

            // 🔧 修复：验证并尝试修复XML内容格式
            if (!IsValidXmlContent(request.Content, request.ProcessingId))
            {
                _logger.LogWarning("[{ProcessingId}] 文件消息XML格式无效，尝试降级处理", request.ProcessingId);
                
                // 对于文件消息，即使XML解析失败，也可以尝试通过其他方式获取
                _logger.LogInformation("[{ProcessingId}] 文件消息将使用备用下载方案", request.ProcessingId);
                // 继续处理，不直接返回失败
            }

            // 调用EYun getMsgFile API
            var downloadRequest = new EYunGetMsgFileRequest
            {
                WId = request.WId,
                MsgId = request.MsgId,
                Content = request.Content
            };

            var downloadResponse = await _eYunDownloadWrapper.GetMsgFileAsync(downloadRequest);
            if (downloadResponse?.Url == null)
            {
                return MediaProcessingResult.CreateFailure("EYun API返回空结果", request.MessageType);
            }

            // 🔧 修复：从XML中提取文件扩展名和原始文件名
            var fileExtension = ExtractFileExtensionFromXml(request.Content);
            var originalFileName = ExtractFileNameFromXml(request.Content);

            // 生成包含正确扩展名的文件名
            var fileName = GenerateFileNameWithExtension(request.MsgId, fileExtension, originalFileName);

            _logger.LogDebug("[{ProcessingId}] 文件命名完成 - OriginalName: {OriginalName}, Extension: {Extension}, GeneratedName: {GeneratedName}",
                request.ProcessingId, originalFileName, fileExtension, fileName);

            // 下载并存储文件
            var storageResult = await DownloadAndStoreFileAsync(downloadResponse.Url, fileName, "files", request.WxManagerId);

            // 清理URL，移除AWS签名参数，保留文件扩展名
            var cleanedUrl = _urlCleaningService.CleanUrl(storageResult.PublicUrl);

            _logger.LogDebug("[{ProcessingId}] 文件URL清理完成 - 原始长度: {OriginalLength}, 清理后长度: {CleanedLength}",
                request.ProcessingId, storageResult.PublicUrl.Length, cleanedUrl.Length);

            return MediaProcessingResult.CreateSuccess(cleanedUrl, fileName, storageResult.FileSize, "file", request.MessageType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 文件消息处理失败", request.ProcessingId);

            var friendlyMessage = _mediaFailureNotificationService.GenerateFailureMessage(
                request.MessageType, ex.Message, $"file_{request.MsgId}");

            // 🔧 增强：为文件处理失败提供降级方案
            var fallbackMessage = "抱歉，我无法直接访问或分析文件内容。不过，如果您能提供具体的问题或描述文件的内容，我很乐意帮助您解答！";
            
            return MediaProcessingResult.CreateFailureWithFriendlyMessage(
                $"文件处理异常: {ex.Message}", fallbackMessage, request.MessageType, $"file_{request.MsgId}");
        }
    }

    /// <summary>
    /// 下载语音文件并转换为mp3格式后存储
    /// </summary>
    private async Task<(string PublicUrl, long FileSize)> DownloadAndConvertVoiceAsync(string fileUrl, string fileName, Guid wxManagerId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("开始下载并转换语音文件 - URL: {FileUrl}, FileName: {FileName}", fileUrl, fileName);

            // 🔧 修复：优化HTTP下载性能和超时配置
            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(30); // 🔧 降低超时时间从5分钟到30秒

            var response = await httpClient.GetAsync(fileUrl, cancellationToken);
            response.EnsureSuccessStatusCode();

            // 🔧 修复：使用流式读取而不是一次性加载到内存
            using var responseStream = await response.Content.ReadAsStreamAsync(cancellationToken);
            using var memoryStream = new MemoryStream();
            await responseStream.CopyToAsync(memoryStream, cancellationToken);
            var silkBytes = memoryStream.ToArray();
            _logger.LogDebug("下载silk文件完成，大小: {Size} bytes", silkBytes.Length);

            // 🔧 验证下载的文件不为空
            if (silkBytes.Length == 0)
            {
                throw new InvalidOperationException("下载的silk文件为空");
            }

            // 2. 创建临时文件进行转换
            var tempSilkPath = Path.GetTempFileName() + ".silk";
            var tempMp3Path = Path.GetTempFileName() + ".mp3";

            try
            {
                // 保存silk文件到临时路径
                await File.WriteAllBytesAsync(tempSilkPath, silkBytes);
                _logger.LogDebug("silk文件已保存到临时路径: {TempPath}, 大小: {Size} bytes", tempSilkPath, silkBytes.Length);

                // 🔧 验证silk文件确实被写入
                var silkFileInfo = new FileInfo(tempSilkPath);
                if (!silkFileInfo.Exists || silkFileInfo.Length == 0)
                {
                    throw new InvalidOperationException($"silk文件写入失败，文件不存在或为空: {tempSilkPath}");
                }

                // 3. 使用Silk音频转换器将silk转换为mp3
                _logger.LogDebug("开始silk到mp3转换 - 输入: {InputPath}, 输出: {OutputPath}", tempSilkPath, tempMp3Path);
                var conversionResult = await _silkAudioConverter.ConvertFromSilkAsync(tempSilkPath, tempMp3Path, AudioFormat.Mp3);

                if (!conversionResult.Success)
                {
                    _logger.LogError("语音转换失败 - 错误: {Error}, 详细信息: {Details}",
                        conversionResult.ErrorMessage, conversionResult.Details ?? "无详细信息");
                    throw new InvalidOperationException($"语音转换失败: {conversionResult.ErrorMessage}");
                }

                _logger.LogDebug("语音转换完成，mp3文件大小: {Size} bytes", conversionResult.OutputFileSize);

                // 🔧 验证转换后的文件
                if (!File.Exists(tempMp3Path))
                {
                    throw new InvalidOperationException("转换后的mp3文件不存在");
                }

                var mp3FileInfo = new FileInfo(tempMp3Path);
                if (mp3FileInfo.Length == 0)
                {
                    throw new InvalidOperationException("转换后的mp3文件为空");
                }

                // 4. 读取转换后的mp3文件
                var mp3Bytes = await File.ReadAllBytesAsync(tempMp3Path);
                _logger.LogDebug("读取mp3文件完成，实际大小: {Size} bytes", mp3Bytes.Length);

                // 5. 构建存储路径并上传
                var mp3FileName = Path.GetFileNameWithoutExtension(fileName) + ".mp3";
                var storageFilePath = $"wx-voices/{wxManagerId}/{DateTime.Now:yyyy/MM/dd}/{mp3FileName}";
                using var fileStream = new MemoryStream(mp3Bytes);
                var uploadResult = await _fileUploadService.UploadFileStreamAsync(fileStream, storageFilePath);

                if (!uploadResult.IsSuccess || string.IsNullOrEmpty(uploadResult.Data))
                {
                    throw new InvalidOperationException($"文件上传失败: {uploadResult.Message}");
                }

                // 🔧 清理URL，移除AWS签名参数
                var cleanUrl = _urlCleaningService.CleanUrl(uploadResult.Data);
                _logger.LogDebug("语音文件上传成功 - 原始URL: {OriginalUrl}, 清理后URL: {CleanUrl}, Size: {Size}",
                    uploadResult.Data, cleanUrl, mp3Bytes.Length);

                return (cleanUrl, mp3Bytes.Length);
            }
            finally
            {
                // 清理临时文件
                try
                {
                    if (File.Exists(tempSilkPath)) File.Delete(tempSilkPath);
                    if (File.Exists(tempMp3Path)) File.Delete(tempMp3Path);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "清理临时文件失败");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "下载并转换语音文件失败 - URL: {FileUrl}, FileName: {FileName}", fileUrl, fileName);
            throw;
        }
    }

    /// <summary>
    /// 下载文件并存储到MinIO/腾讯COS - 增强版，确保ObjectSize正确处理
    /// </summary>
    private async Task<(string PublicUrl, long FileSize)> DownloadAndStoreFileAsync(string fileUrl, string fileName, string category, Guid wxManagerId)
    {
        try
        {
            _logger.LogDebug("开始下载并存储文件 - URL: {FileUrl}, FileName: {FileName}, Category: {Category}", 
                fileUrl, fileName, category);

            // 🔧 修复：增强HTTP客户端配置
            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromMinutes(2); // 增加超时时间以支持大文件
            httpClient.DefaultRequestHeaders.Add("User-Agent", "HappyWechat-MediaProcessor/1.0");

            // 1. 获取文件响应
            using var response = await httpClient.GetAsync(fileUrl, HttpCompletionOption.ResponseHeadersRead);
            response.EnsureSuccessStatusCode();

            var contentType = response.Content.Headers.ContentType?.MediaType ?? "application/octet-stream";
            var contentLength = response.Content.Headers.ContentLength;
            
            _logger.LogDebug("HTTP响应接收成功 - ContentType: {ContentType}, ContentLength: {ContentLength}", 
                contentType, contentLength);

            // 2. 构建存储路径
            var storageFilePath = $"wx-{category}/{wxManagerId}/{DateTime.Now:yyyy/MM/dd}/{fileName}";

            // 🔧 修复：增强流处理，确保与MinIO ObjectSize逻辑兼容
            using var responseStream = await response.Content.ReadAsStreamAsync();
            
            // 如果HTTP头没有提供文件大小，我们需要先缓存流来确定大小
            Stream processStream = responseStream;
            long actualFileSize = contentLength ?? 0;
            
            if (actualFileSize <= 0)
            {
                _logger.LogInformation("HTTP响应头未提供文件大小，复制到MemoryStream确定大小 - URL: {FileUrl}", fileUrl);
                
                var memoryStream = new MemoryStream();
                await responseStream.CopyToAsync(memoryStream);
                actualFileSize = memoryStream.Length;
                memoryStream.Position = 0;
                processStream = memoryStream;
                
                _logger.LogInformation("通过流复制确定文件大小: {FileSize} bytes - {FileName}", actualFileSize, fileName);
            }
            
            // 验证文件大小
            if (actualFileSize <= 0)
            {
                throw new InvalidOperationException($"无法确定文件大小，下载失败 - URL: {fileUrl}, FileName: {fileName}");
            }
            
            // 3. 上传到存储服务
            _logger.LogDebug("开始上传到存储服务 - Path: {StorageFilePath}, Size: {FileSize} bytes", 
                storageFilePath, actualFileSize);
                
            var uploadResult = await _fileUploadService.UploadFileStreamAsync(processStream, storageFilePath);
            
            // 清理临时MemoryStream
            if (processStream != responseStream)
            {
                processStream.Dispose();
            }

            if (!uploadResult.IsSuccess || string.IsNullOrEmpty(uploadResult.Data))
            {
                throw new InvalidOperationException($"文件上传失败: {uploadResult.Message}");
            }

            _logger.LogInformation("文件下载并存储成功 - URL: {PublicUrl}, Size: {FileSize} bytes - {FileName}", 
                uploadResult.Data, actualFileSize, fileName);
                
            return (uploadResult.Data, actualFileSize);
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP请求失败 - URL: {FileUrl}, FileName: {FileName}", fileUrl, fileName);
            throw new InvalidOperationException($"下载文件失败: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "下载文件超时 - URL: {FileUrl}, FileName: {FileName}", fileUrl, fileName);
            throw new InvalidOperationException($"下载文件超时: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "下载并存储文件失败 - URL: {FileUrl}, FileName: {FileName}", fileUrl, fileName);
            throw;
        }
    }

    /// <summary>
    /// 解析语音消息XML获取必要参数（增强版）
    /// </summary>
    private VoiceMessageInfo? ParseVoiceMessageXml(string xmlContent)
    {
        try
        {
            if (string.IsNullOrEmpty(xmlContent))
            {
                _logger.LogWarning("语音消息XML内容为空");
                return null;
            }

            _logger.LogInformation("🔧 开始解析语音消息XML - Content: {XmlContent}",
                xmlContent.Substring(0, Math.Min(300, xmlContent.Length)));

            // 使用XDocument进行健壮的XML解析
            XDocument doc;
            try
            {
                doc = XDocument.Parse(xmlContent);
            }
            catch (XmlException ex)
            {
                _logger.LogWarning(ex, "XML格式无效，尝试修复 - Content: {XmlContent}", xmlContent);

                // 尝试修复常见的XML格式问题
                var fixedXml = FixCommonXmlIssues(xmlContent);
                doc = XDocument.Parse(fixedXml);
            }

            // 尝试多种可能的XML结构
            var voiceInfo = TryParseVoiceInfo(doc);

            if (voiceInfo != null)
            {
                _logger.LogInformation("✅ 语音消息XML解析成功 - Length: {Length}, BufId: {BufId}, Source: {Source}",
                    voiceInfo.Length, voiceInfo.BufId, voiceInfo.Source);
                return voiceInfo;
            }

            _logger.LogWarning("无法从XML中提取语音信息 - Content: {XmlContent}", xmlContent);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析语音消息XML失败 - Content: {XmlContent}", xmlContent);
            return null;
        }
    }

    /// <summary>
    /// 🔧 重构：专门解析语音消息XML结构 - 语音消息使用自闭合voicemsg标签
    /// </summary>
    private VoiceMessageInfo? TryParseVoiceInfo(XDocument doc)
    {
        _logger.LogDebug("开始解析语音XML结构");

        // 🔧 语音消息标准结构: <msg><voicemsg length="1548" bufid="0" voicelength="1139" ... /></msg>
        var voiceMsg = doc.Element("msg")?.Element("voicemsg");
        if (voiceMsg != null)
        {
            _logger.LogDebug("找到voicemsg标签，开始提取属性");

            // 提取所有相关属性
            var lengthAttr = voiceMsg.Attribute("length");
            var voiceLengthAttr = voiceMsg.Attribute("voicelength");
            var bufIdAttr = voiceMsg.Attribute("bufid");
            var aesKeyAttr = voiceMsg.Attribute("aeskey");
            var voiceUrlAttr = voiceMsg.Attribute("voiceurl");
            var fromUserAttr = voiceMsg.Attribute("fromusername");

            _logger.LogDebug("语音属性提取结果 - length: {Length}, voicelength: {VoiceLength}, bufid: {BufId}, aeskey: {AesKey}, fromusername: {FromUser}",
                lengthAttr?.Value, voiceLengthAttr?.Value, bufIdAttr?.Value,
                aesKeyAttr?.Value?.Substring(0, Math.Min(8, aesKeyAttr?.Value?.Length ?? 0)),
                fromUserAttr?.Value);

            // 验证必要参数：length和bufid
            if (lengthAttr != null && bufIdAttr != null &&
                int.TryParse(lengthAttr.Value, out var length) && length > 0)
            {
                _logger.LogInformation("✅ 语音XML解析成功 - Length: {Length}, BufId: {BufId}, VoiceLength: {VoiceLength}",
                    length, bufIdAttr.Value, voiceLengthAttr?.Value);

                return new VoiceMessageInfo
                {
                    Length = length,
                    BufId = bufIdAttr.Value,
                    Source = "XML解析-voicemsg标签"
                };
            }
            else
            {
                _logger.LogWarning("语音XML属性验证失败 - length: {Length}, bufid: {BufId}",
                    lengthAttr?.Value, bufIdAttr?.Value);
            }
        }
        else
        {
            _logger.LogWarning("未找到voicemsg标签，尝试其他解析方式");
        }

        // 备用方案：检查是否有其他可能的结构
        var rootElement = doc.Root;
        if (rootElement != null)
        {
            _logger.LogDebug("尝试从根元素解析语音属性");

            var lengthAttr = rootElement.Attribute("length") ?? rootElement.Attribute("voicelength");
            var bufIdAttr = rootElement.Attribute("bufid") ?? rootElement.Attribute("bufId");

            if (lengthAttr != null && bufIdAttr != null &&
                int.TryParse(lengthAttr.Value, out var length) && length > 0)
            {
                _logger.LogInformation("✅ 从根元素解析语音参数成功 - Length: {Length}, BufId: {BufId}",
                    length, bufIdAttr.Value);

                return new VoiceMessageInfo
                {
                    Length = length,
                    BufId = bufIdAttr.Value,
                    Source = "XML解析-根元素属性"
                };
            }
        }

        _logger.LogWarning("所有语音XML解析方案都失败");
        return null;
    }

    /// <summary>
    /// 修复常见的XML格式问题（增强版）
    /// </summary>
    private string FixCommonXmlIssues(string xmlContent)
    {
        if (string.IsNullOrEmpty(xmlContent))
            return xmlContent;

        // 移除可能的BOM
        if (xmlContent.StartsWith("\uFEFF"))
        {
            xmlContent = xmlContent.Substring(1);
        }

        // 🔧 新增：处理不完整的XML声明
        if (!xmlContent.TrimStart().StartsWith("<"))
        {
            xmlContent = $"<root>{xmlContent}</root>";
        }
        
        // 🔧 新增：为不带XML声明的内容添加声明（如果需要）
        var trimmed = xmlContent.TrimStart();
        if (trimmed.StartsWith("<msg") && !trimmed.StartsWith("<?xml"))
        {
            xmlContent = $"<?xml version=\"1.0\" encoding=\"UTF-8\"?>{Environment.NewLine}{xmlContent}";
        }

        // 转义特殊字符（增强版，处理更多情况）
        if (!xmlContent.Contains("&amp;"))
        {
            // 只转义未转义的&符号
            xmlContent = System.Text.RegularExpressions.Regex.Replace(
                xmlContent, 
                @"&(?!(amp|lt|gt|quot|apos);)", 
                "&amp;");
        }

        // 🔧 新增：修复自闭合标签问题
        xmlContent = System.Text.RegularExpressions.Regex.Replace(
            xmlContent,
            @"<(\w+)([^>]*?)(?<!/)>",
            match => {
                var tagName = match.Groups[1].Value;
                var attributes = match.Groups[2].Value;
                
                // 如果是已知的自闭合标签，确保正确闭合
                if (new[] { "voicemsg", "img", "file" }.Contains(tagName.ToLower()))
                {
                    if (!attributes.TrimEnd().EndsWith("/"))
                    {
                        return $"<{tagName}{attributes} />";
                    }
                }
                return match.Value;
            });

        return xmlContent;
    }

    /// <summary>
    /// 🔧 新增：从回调数据中提取语音参数（优先方案）
    /// </summary>
    private VoiceMessageInfo? ExtractVoiceParametersFromCallback(MediaDownloadRequest request)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 尝试从回调数据提取语音参数", request.ProcessingId);

            // 🔧 修复：优先使用原始回调数据中的直接参数
            if (request.OriginalCallback?.Data != null)
            {
                var data = request.OriginalCallback.Data;
                var length = data.Length ?? 0;
                var bufId = data.BufId;

                _logger.LogDebug("[{ProcessingId}] 从原始回调数据获取参数 - Length: {Length}, BufId: {BufId}",
                    request.ProcessingId, length, bufId);

                if (length > 0 && !string.IsNullOrEmpty(bufId))
                {
                    _logger.LogInformation("[{ProcessingId}] ✅ 从原始回调数据提取语音参数成功 - Length: {Length}, BufId: {BufId}",
                        request.ProcessingId, length, bufId);

                    return new VoiceMessageInfo
                    {
                        Length = length,
                        BufId = bufId,
                        Source = "原始回调数据"
                    };
                }
                else
                {
                    _logger.LogWarning("[{ProcessingId}] 原始回调数据中的语音参数无效 - Length: {Length}, BufId: {BufId}",
                        request.ProcessingId, length, bufId);
                }
            }

            // 备用方案：如果Content包含JSON格式的回调数据，尝试解析
            if (!string.IsNullOrEmpty(request.Content) && request.Content.TrimStart().StartsWith("{"))
            {
                try
                {
                    using var jsonDoc = System.Text.Json.JsonDocument.Parse(request.Content);
                    var root = jsonDoc.RootElement;

                    if (root.TryGetProperty("data", out var dataElement))
                    {
                        var length = dataElement.TryGetProperty("length", out var lengthElement) && lengthElement.TryGetInt32(out var len) ? len : 0;
                        var bufId = dataElement.TryGetProperty("bufId", out var bufIdElement) ? bufIdElement.GetString() : null;

                        if (length > 0 && !string.IsNullOrEmpty(bufId))
                        {
                            _logger.LogInformation("[{ProcessingId}] ✅ 从JSON回调数据提取语音参数成功 - Length: {Length}, BufId: {BufId}",
                                request.ProcessingId, length, bufId);

                            return new VoiceMessageInfo
                            {
                                Length = length,
                                BufId = bufId,
                                Source = "JSON回调数据"
                            };
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "[{ProcessingId}] JSON解析失败，继续尝试其他方案", request.ProcessingId);
                }
            }

            _logger.LogDebug("[{ProcessingId}] 无法从回调数据提取语音参数", request.ProcessingId);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 从回调数据提取语音参数异常", request.ProcessingId);
            return null;
        }
    }

    /// <summary>
    /// 🔧 新增：验证和修复语音参数
    /// </summary>
    private VoiceMessageInfo? ValidateAndFixVoiceParameters(VoiceMessageInfo voiceInfo, MediaDownloadRequest request)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 开始验证语音参数 - Length: {Length}, BufId: {BufId}",
                request.ProcessingId, voiceInfo.Length, voiceInfo.BufId);

            // 验证Length参数
            if (voiceInfo.Length <= 0)
            {
                _logger.LogWarning("[{ProcessingId}] 语音长度无效: {Length}，尝试修复", request.ProcessingId, voiceInfo.Length);

                // 尝试从XML中获取voicelength作为备用
                var xmlVoiceInfo = ParseVoiceMessageXml(request.Content);
                if (xmlVoiceInfo?.Length > 0)
                {
                    voiceInfo.Length = xmlVoiceInfo.Length;
                    _logger.LogInformation("[{ProcessingId}] ✅ 从XML修复Length参数: {Length}", request.ProcessingId, voiceInfo.Length);
                }
                else
                {
                    _logger.LogError("[{ProcessingId}] ❌ 无法修复Length参数", request.ProcessingId);
                    return null;
                }
            }

            // 🔧 修复：验证BufId参数 - bufId="0"是有效的，只有空值才需要修复
            if (string.IsNullOrEmpty(voiceInfo.BufId))
            {
                _logger.LogWarning("[{ProcessingId}] BufId为空，尝试修复", request.ProcessingId);

                // 尝试从XML中获取BufId
                var xmlVoiceInfo = ParseVoiceMessageXml(request.Content);
                if (!string.IsNullOrEmpty(xmlVoiceInfo?.BufId))
                {
                    voiceInfo.BufId = xmlVoiceInfo.BufId;
                    _logger.LogInformation("[{ProcessingId}] ✅ 从XML修复BufId参数: {BufId}", request.ProcessingId, voiceInfo.BufId);
                }
                else
                {
                    // 最后的备用方案：使用MsgId作为替代
                    voiceInfo.BufId = request.MsgId.ToString();
                    _logger.LogWarning("[{ProcessingId}] ⚠️ 使用MsgId作为BufId替代方案: {BufId}", request.ProcessingId, voiceInfo.BufId);
                }
            }
            else
            {
                _logger.LogDebug("[{ProcessingId}] BufId参数有效: {BufId}", request.ProcessingId, voiceInfo.BufId);
            }

            _logger.LogInformation("[{ProcessingId}] ✅ 语音参数验证通过 - Length: {Length}, BufId: {BufId}",
                request.ProcessingId, voiceInfo.Length, voiceInfo.BufId);

            return voiceInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 语音参数验证异常", request.ProcessingId);
            return null;
        }
    }

    /// <summary>
    /// 🔧 新增：尝试从原始内容中提取语音信息（降级方案）
    /// </summary>
    private VoiceMessageInfo? TryExtractVoiceInfoFromRawContent(string rawContent, string processingId)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 尝试从原始内容提取语音信息 - Content: {Content}", 
                processingId, rawContent.Substring(0, Math.Min(100, rawContent.Length)));

            // 方法1：使用正则表达式提取关键参数
            var lengthMatch = System.Text.RegularExpressions.Regex.Match(rawContent, @"length[""'=:\s]*(\d+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            var bufIdMatch = System.Text.RegularExpressions.Regex.Match(rawContent, @"bufid[""'=:\s]*[""']?(\d+)[""']?", System.Text.RegularExpressions.RegexOptions.IgnoreCase);

            if (lengthMatch.Success && bufIdMatch.Success &&
                int.TryParse(lengthMatch.Groups[1].Value, out var length) &&
                !string.IsNullOrEmpty(bufIdMatch.Groups[1].Value))
            {
                _logger.LogInformation("[{ProcessingId}] 正则表达式提取成功 - Length: {Length}, BufId: {BufId}", 
                    processingId, length, bufIdMatch.Groups[1].Value);
                    
                return new VoiceMessageInfo
                {
                    Length = length,
                    BufId = bufIdMatch.Groups[1].Value,
                    Source = "正则表达式提取"
                };
            }

            // 方法2：尝试JSON解析（如果原始内容是JSON格式）
            if (rawContent.TrimStart().StartsWith("{"))
            {
                try
                {
                    using var jsonDoc = System.Text.Json.JsonDocument.Parse(rawContent);
                    var root = jsonDoc.RootElement;
                    
                    if (root.TryGetProperty("length", out var lengthElement) &&
                        root.TryGetProperty("bufId", out var bufIdElement))
                    {
                        if (lengthElement.TryGetInt32(out var jsonLength) &&
                            bufIdElement.ValueKind == System.Text.Json.JsonValueKind.String)
                        {
                            var jsonBufId = bufIdElement.GetString();
                            if (!string.IsNullOrEmpty(jsonBufId))
                            {
                                _logger.LogInformation("[{ProcessingId}] JSON解析提取成功 - Length: {Length}, BufId: {BufId}", 
                                    processingId, jsonLength, jsonBufId);
                                    
                                return new VoiceMessageInfo
                                {
                                    Length = jsonLength,
                                    BufId = jsonBufId,
                                    Source = "JSON降级解析"
                                };
                            }
                        }
                    }
                }
                catch (System.Text.Json.JsonException ex)
                {
                    _logger.LogDebug(ex, "[{ProcessingId}] JSON解析失败，继续其他方法", processingId);
                }
            }

            _logger.LogWarning("[{ProcessingId}] 所有降级方案都无法提取语音信息", processingId);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 降级提取语音信息时发生异常", processingId);
            return null;
        }
    }

    /// <summary>
    /// 🔧 新增：从XML中提取文件扩展名
    /// </summary>
    private string ExtractFileExtensionFromXml(string xmlContent)
    {
        try
        {
            if (string.IsNullOrEmpty(xmlContent))
            {
                _logger.LogWarning("XML内容为空，无法提取文件扩展名");
                return string.Empty;
            }

            _logger.LogDebug("开始从XML提取文件扩展名 - Content: {XmlContent}", xmlContent);

            // 使用XDocument进行健壮的XML解析
            XDocument doc;
            try
            {
                doc = XDocument.Parse(xmlContent);
            }
            catch (XmlException ex)
            {
                _logger.LogWarning(ex, "XML格式无效，尝试修复后重新解析");
                var fixedXml = FixCommonXmlIssues(xmlContent);
                doc = XDocument.Parse(fixedXml);
            }

            // 查找fileext标签
            var fileExtElement = doc.Descendants("fileext").FirstOrDefault();
            if (fileExtElement != null && !string.IsNullOrEmpty(fileExtElement.Value))
            {
                var extension = fileExtElement.Value.Trim();
                // 确保扩展名以点开头
                if (!extension.StartsWith("."))
                {
                    extension = "." + extension;
                }

                _logger.LogDebug("从fileext标签提取到扩展名: {Extension}", extension);
                return ValidateFileExtension(extension);
            }

            _logger.LogDebug("未找到fileext标签，尝试从title标签提取扩展名");
            return ExtractExtensionFromTitle(doc);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从XML提取文件扩展名失败");
            return string.Empty;
        }
    }

    /// <summary>
    /// 🔧 新增：从XML的title标签中提取文件名和扩展名
    /// </summary>
    private string ExtractFileNameFromXml(string xmlContent)
    {
        try
        {
            if (string.IsNullOrEmpty(xmlContent))
                return string.Empty;

            var doc = XDocument.Parse(xmlContent);
            var titleElement = doc.Descendants("title").FirstOrDefault();

            if (titleElement != null && !string.IsNullOrEmpty(titleElement.Value))
            {
                var fileName = titleElement.Value.Trim();
                _logger.LogDebug("从title标签提取到文件名: {FileName}", fileName);
                return fileName;
            }

            return string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "从XML提取文件名失败");
            return string.Empty;
        }
    }

    /// <summary>
    /// 🔧 新增：从title标签中提取扩展名作为备用方案
    /// </summary>
    private string ExtractExtensionFromTitle(XDocument doc)
    {
        try
        {
            var titleElement = doc.Descendants("title").FirstOrDefault();
            if (titleElement != null && !string.IsNullOrEmpty(titleElement.Value))
            {
                var fileName = titleElement.Value.Trim();
                var extension = Path.GetExtension(fileName);

                if (!string.IsNullOrEmpty(extension))
                {
                    _logger.LogDebug("从title标签提取到扩展名: {Extension}", extension);
                    return ValidateFileExtension(extension);
                }
            }

            _logger.LogDebug("无法从title标签提取扩展名");
            return string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "从title标签提取扩展名失败");
            return string.Empty;
        }
    }

    /// <summary>
    /// 🔧 新增：验证文件扩展名的安全性
    /// </summary>
    private string ValidateFileExtension(string extension)
    {
        if (string.IsNullOrEmpty(extension))
            return string.Empty;

        // 转换为小写并确保以点开头
        extension = extension.ToLowerInvariant();
        if (!extension.StartsWith("."))
        {
            extension = "." + extension;
        }

        // 危险扩展名黑名单
        var dangerousExtensions = new[]
        {
            ".exe", ".bat", ".cmd", ".com", ".pif", ".scr", ".vbs", ".js", ".jar",
            ".msi", ".dll", ".sys", ".drv", ".ocx", ".cpl", ".inf", ".reg"
        };

        if (dangerousExtensions.Contains(extension))
        {
            _logger.LogWarning("检测到危险文件扩展名，已过滤: {Extension}", extension);
            return string.Empty;
        }

        // 允许的文件扩展名白名单
        var allowedExtensions = new[]
        {
            ".txt", ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg",
            ".mp3", ".wav", ".mp4", ".avi", ".mov", ".wmv", ".flv",
            ".zip", ".rar", ".7z", ".tar", ".gz", ".json", ".xml", ".csv"
        };

        if (allowedExtensions.Contains(extension))
        {
            return extension;
        }

        _logger.LogWarning("文件扩展名不在允许列表中: {Extension}", extension);
        return string.Empty;
    }

    /// <summary>
    /// 🔧 新增：生成包含扩展名的文件名
    /// </summary>
    private string GenerateFileNameWithExtension(long msgId, string extension, string originalFileName = "")
    {
        var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
        var baseName = $"file_{msgId}_{timestamp}";

        // 如果有有效的扩展名，直接使用
        if (!string.IsNullOrEmpty(extension))
        {
            return baseName + extension;
        }

        // 如果没有扩展名但有原始文件名，尝试从原始文件名提取
        if (!string.IsNullOrEmpty(originalFileName))
        {
            var extractedExtension = Path.GetExtension(originalFileName);
            if (!string.IsNullOrEmpty(extractedExtension))
            {
                var validatedExtension = ValidateFileExtension(extractedExtension);
                if (!string.IsNullOrEmpty(validatedExtension))
                {
                    return baseName + validatedExtension;
                }
            }
        }

        // 如果都没有，返回不带扩展名的文件名（保持向后兼容）
        _logger.LogWarning("无法确定文件扩展名，使用无扩展名文件名 - MsgId: {MsgId}", msgId);
        return baseName;
    }

    /// <summary>
    /// 🔧 修复：验证XML内容格式（支持容错处理）
    /// </summary>
    private bool IsValidXmlContent(string content, string processingId)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(content))
            {
                _logger.LogWarning("[{ProcessingId}] XML内容为空", processingId);
                return false;
            }

            var trimmedContent = content.TrimStart();
            
            // 🔧 容错处理：支持不带XML声明的格式
            if (!trimmedContent.StartsWith("<?xml") && !trimmedContent.StartsWith("<"))
            {
                _logger.LogWarning("[{ProcessingId}] 内容不是XML格式 - 开头: {Start}",
                    processingId, trimmedContent.Substring(0, Math.Min(50, trimmedContent.Length)));
                return false;
            }

            // 尝试解析XML以验证格式（包含容错修复）
            try
            {
                XDocument.Parse(content);
                _logger.LogDebug("[{ProcessingId}] XML内容格式验证通过", processingId);
                return true;
            }
            catch (XmlException ex)
            {
                // 🔧 尝试修复常见XML问题后重新验证
                try
                {
                    var fixedXml = FixCommonXmlIssues(content);
                    XDocument.Parse(fixedXml);
                    _logger.LogInformation("[{ProcessingId}] XML格式修复后验证通过", processingId);
                    return true;
                }
                catch (XmlException)
                {
                    _logger.LogWarning(ex, "[{ProcessingId}] XML格式验证失败，无法修复", processingId);
                    return false;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] XML内容验证异常", processingId);
            return false;
        }
    }

    /// <summary>
    /// 🔧 修复：提取原始XML内容（处理JSON包装的情况）
    /// </summary>
    private string ExtractOriginalXmlContent(string content, string processingId)
    {
        try
        {
            if (string.IsNullOrEmpty(content))
            {
                _logger.LogWarning("[{ProcessingId}] Content为空", processingId);
                return content;
            }

            // 检查是否为JSON格式（以{开头）
            if (!content.TrimStart().StartsWith("{"))
            {
                // 不是JSON，直接返回原内容（可能已经是XML）
                _logger.LogDebug("[{ProcessingId}] Content不是JSON格式，直接使用 - Length: {Length}",
                    processingId, content.Length);
                return content;
            }

            // 尝试解析JSON并提取original_content
            try
            {
                using var jsonDoc = System.Text.Json.JsonDocument.Parse(content);
                if (jsonDoc.RootElement.TryGetProperty("original_content", out var originalContentElement))
                {
                    var originalContent = originalContentElement.GetString();
                    if (!string.IsNullOrEmpty(originalContent))
                    {
                        _logger.LogDebug("[{ProcessingId}] 从JSON中提取原始XML内容 - OriginalLength: {OriginalLength}, ExtractedLength: {ExtractedLength}",
                            processingId, content.Length, originalContent.Length);
                        return originalContent;
                    }
                }

                _logger.LogWarning("[{ProcessingId}] JSON中未找到original_content字段", processingId);
                return content; // 返回原内容作为降级方案
            }
            catch (System.Text.Json.JsonException ex)
            {
                // 降级到Newtonsoft.Json
                try
                {
                    var jsonObj = Newtonsoft.Json.JsonConvert.DeserializeObject<System.Collections.Generic.Dictionary<string, object>>(content);
                    if (jsonObj != null && jsonObj.TryGetValue("original_content", out var originalContentObj))
                    {
                        var originalContent = originalContentObj?.ToString();
                        if (!string.IsNullOrEmpty(originalContent))
                        {
                            _logger.LogDebug("[{ProcessingId}] 从JSON中提取原始XML内容(降级方案) - OriginalLength: {OriginalLength}, ExtractedLength: {ExtractedLength}",
                                processingId, content.Length, originalContent.Length);
                            return originalContent;
                        }
                    }
                }
                catch (Newtonsoft.Json.JsonException newtonsoftEx)
                {
                    _logger.LogWarning(newtonsoftEx, "[{ProcessingId}] Newtonsoft.Json解析也失败，使用原内容", processingId);
                }

                _logger.LogWarning(ex, "[{ProcessingId}] JSON解析失败，使用原内容", processingId);
                return content; // JSON解析失败，返回原内容
            }

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 提取原始XML内容失败", processingId);
            return content; // 出错时返回原内容
        }
    }

    /// <summary>
    /// 🔧 修复：获取正确的EYun WId参数
    /// </summary>
    private async Task<string?> GetCorrectWIdAsync(Guid wxManagerId, string fallbackWcId, string processingId)
    {
        try
        {
            // 如果有WxManagerId，通过数据库查询获取正确的WId
            if (wxManagerId != Guid.Empty)
            {
                using var context = await _dbContextFactory.CreateDbContextAsync();
                var manager = await context.WxMangerEntities
                    .Where(m => m.Id == wxManagerId && m.IsEnabled)
                    .Select(m => new { m.WId, m.WcId })
                    .FirstOrDefaultAsync();

                if (manager?.WId != null)
                {
                    _logger.LogDebug("[{ProcessingId}] 通过WxManagerId获取WId成功 - WxManagerId: {WxManagerId}, WId: {WId}",
                        processingId, wxManagerId, manager.WId);
                    return manager.WId;
                }
            }

            // 降级方案：通过WcId查询WId
            if (!string.IsNullOrEmpty(fallbackWcId))
            {
                using var context = await _dbContextFactory.CreateDbContextAsync();
                var manager = await context.WxMangerEntities
                    .Where(m => m.WcId == fallbackWcId && m.IsEnabled)
                    .Select(m => new { m.WId, m.Id })
                    .FirstOrDefaultAsync();

                if (manager?.WId != null)
                {
                    _logger.LogDebug("[{ProcessingId}] 通过WcId获取WId成功 - WcId: {WcId}, WId: {WId}",
                        processingId, fallbackWcId, manager.WId);
                    return manager.WId;
                }
            }

            _logger.LogWarning("[{ProcessingId}] 无法获取有效的WId - WxManagerId: {WxManagerId}, WcId: {WcId}",
                processingId, wxManagerId, fallbackWcId);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] 获取WId失败 - WxManagerId: {WxManagerId}, WcId: {WcId}",
                processingId, wxManagerId, fallbackWcId);
            return null;
        }
    }

    /// <summary>
    /// 🔧 新增：判断是否为群组文件消息（需要缓存的消息类型）
    /// </summary>
    private static bool IsGroupFileMessage(string messageType)
    {
        return messageType is "80002" or "80009"; // 群聊图片和群聊文件
    }
}

/// <summary>
/// 媒体类型信息
/// </summary>
internal record MediaTypeInfo(string MediaType, string ApiEndpoint, string DefaultExtension, bool RequiresFormatConversion);

/// <summary>
/// 语音消息信息
/// </summary>
internal class VoiceMessageInfo
{
    public int Length { get; set; }
    public string BufId { get; set; } = string.Empty;

    /// <summary>
    /// 参数来源（用于调试）
    /// </summary>
    public string Source { get; set; } = "未知";
}
