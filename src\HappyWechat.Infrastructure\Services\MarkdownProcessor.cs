using Microsoft.Extensions.Logging;
using HappyWechat.Application.Interfaces;
using HappyWechat.Application.DTOs.MessageProcess;
using System.Text.RegularExpressions;

namespace HappyWechat.Infrastructure.Services;

/// <summary>
/// Markdown格式处理器实现 - 精简版本，只处理标题#、加粗**、列表*
/// </summary>
public class MarkdownProcessor : IMarkdownProcessor
{
    private readonly ILogger<MarkdownProcessor> _logger;

    // 🚀 新架构：增强正则表达式，支持多种图片和文件链接格式
    private static readonly Regex HeadingRegex = new(@"^#{1,6}\s*", RegexOptions.Multiline | RegexOptions.Compiled);
    private static readonly Regex BoldRegex = new(@"\*\*(.+?)\*\*", RegexOptions.Compiled);
    private static readonly Regex ListItemRegex = new(@"^\s*\*\s+", RegexOptions.Multiline | RegexOptions.Compiled);

    // 图片链接相关正则表达式
    private static readonly Regex ImageLinkRegex = new(@"图片链接[：:]\s*(https?://[^\s\n]+)", RegexOptions.IgnoreCase | RegexOptions.Compiled);
    private static readonly Regex MarkdownImageRegex = new(@"!\[.*?\]\((https?://[^\s)]+)\)", RegexOptions.IgnoreCase | RegexOptions.Compiled);
    private static readonly Regex HtmlImageRegex = new(@"<img[^>]+src=[""']([^""']+)[""'][^>]*>", RegexOptions.IgnoreCase | RegexOptions.Compiled);
    private static readonly Regex DirectImageUrlRegex = new(@"(https?://[^\s]+\.(?:jpg|jpeg|png|gif|webp|bmp|svg)(?:\?[^\s]*)?)", RegexOptions.IgnoreCase | RegexOptions.Compiled);
    private static readonly Regex CozeImageRegex = new(@"(https?://s\.coze\.cn/[^\s]+)", RegexOptions.IgnoreCase | RegexOptions.Compiled);

    // 文件链接相关正则表达式
    private static readonly Regex FileLinkRegex = new(@"文件链接[：:]\s*(https?://[^\s\n]+)", RegexOptions.IgnoreCase | RegexOptions.Compiled);
    private static readonly Regex DirectFileUrlRegex = new(@"(https?://[^\s]+\.(?:pdf|doc|docx|xls|xlsx|ppt|pptx|txt|zip|rar|7z)(?:\?[^\s]*)?)", RegexOptions.IgnoreCase | RegexOptions.Compiled);

    private static readonly Regex UrlRegex = new(@"https?://[^\s\n]+", RegexOptions.Compiled);

    public MarkdownProcessor(ILogger<MarkdownProcessor> logger)
    {
        _logger = logger;
    }

    public Task<MarkdownProcessingResult> CleanAiResponseAsync(string aiResponse, MarkdownProcessingOptions? options = null)
    {
        if (string.IsNullOrWhiteSpace(aiResponse))
        {
            return Task.FromResult(MarkdownProcessingResult.Failure(aiResponse ?? string.Empty, "输入内容为空"));
        }

        try
        {
            // _logger.LogDebug("📝 开始精简清理AI响应，原始长度: {Length}", aiResponse.Length);
            
            var content = aiResponse;

            // 1. 清理标题 # - 移除开头的#符号，保留标题内容
            content = HeadingRegex.Replace(content, "");

            // 2. 清理加粗 ** - 移除**符号，保留加粗内容
            content = BoldRegex.Replace(content, "$1");

            // 3. 清理列表 * - 移除行首的*符号，保留列表内容
            content = ListItemRegex.Replace(content, "");

            // 4. 清理多余空格（只清理连续空格，保留换行符和段落结构）
            content = Regex.Replace(content, @" {2,}", " ");

            // 5. 清理首尾空白
            content = content.Trim();

            var result = MarkdownProcessingResult.Success(aiResponse, content);

            _logger.LogDebug("✅ 精简清理完成 - 原始长度: {OriginalLength}, 处理后长度: {ProcessedLength}", 
                aiResponse.Length, content.Length);

            return Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 清理AI响应时发生错误");
            return Task.FromResult(MarkdownProcessingResult.Failure(aiResponse, ex.Message));
        }
    }

    /// <summary>
    /// 🚀 新架构：处理Markdown格式，支持图片链接识别
    /// </summary>
    public async Task<string> ProcessMarkdownAsync(string content)
    {
        if (string.IsNullOrWhiteSpace(content))
        {
            return string.Empty;
        }

        try
        {
            _logger.LogInformation("📝 开始Markdown处理 - 原始长度: {Length}", content.Length);

            // 1. 移除Markdown格式符号
            var processed = content;

            // 移除标题符号
            processed = HeadingRegex.Replace(processed, "");

            // 移除加粗符号，保留内容
            processed = BoldRegex.Replace(processed, "$1");

            // 移除列表符号
            processed = ListItemRegex.Replace(processed, "");

            // 2. 处理图片链接格式
            processed = ProcessImageLinks(processed);

            // 3. 清理多余的空行和空格
            processed = CleanupWhitespace(processed);

            _logger.LogInformation("📝 Markdown处理完成 - 处理后长度: {Length}", processed.Length);

            return processed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Markdown处理异常");
            return content; // 异常时返回原始内容
        }
    }

    /// <summary>
    /// 处理图片链接格式 - 增强版本，支持多种格式
    /// </summary>
    private string ProcessImageLinks(string content)
    {
        // 1. 处理"图片链接：https://..."格式
        content = ImageLinkRegex.Replace(content, match =>
        {
            var url = match.Groups[1].Value;
            return $"\n图片链接：{url}"; // 保持标准格式
        });

        // 2. 处理Markdown格式的图片 ![alt](url)
        content = MarkdownImageRegex.Replace(content, match =>
        {
            var url = match.Groups[1].Value;
            return $"\n图片链接：{url}";
        });

        // 3. 处理HTML格式的图片 <img src="url">
        content = HtmlImageRegex.Replace(content, match =>
        {
            var url = match.Groups[1].Value;
            return $"\n图片链接：{url}";
        });

        // 4. 处理Coze平台图片链接
        content = CozeImageRegex.Replace(content, match =>
        {
            var url = match.Groups[1].Value;
            return $"\n图片链接：{url}";
        });

        // 5. 处理直接的图片URL（常见图片扩展名）
        content = DirectImageUrlRegex.Replace(content, match =>
        {
            var url = match.Groups[1].Value;
            // 检查是否已经被处理过（避免重复处理）
            if (!content.Contains($"图片链接：{url}"))
            {
                return $"\n图片链接：{url}";
            }
            return match.Value;
        });

        // 6. 处理"文件链接：https://..."格式
        content = FileLinkRegex.Replace(content, match =>
        {
            var url = match.Groups[1].Value;
            return $"\n文件链接：{url}";
        });

        // 7. 处理直接的文件URL（常见文件扩展名）
        content = DirectFileUrlRegex.Replace(content, match =>
        {
            var url = match.Groups[1].Value;
            // 检查是否已经被处理过（避免重复处理）
            if (!content.Contains($"文件链接：{url}"))
            {
                return $"\n文件链接：{url}";
            }
            return match.Value;
        });

        return content;
    }

    /// <summary>
    /// 清理多余的空白字符
    /// </summary>
    private string CleanupWhitespace(string content)
    {
        // 移除多余的空行（保留最多一个空行）
        content = Regex.Replace(content, @"\n\s*\n\s*\n", "\n\n");

        // 移除行首行尾空格
        var lines = content.Split('\n');
        for (int i = 0; i < lines.Length; i++)
        {
            lines[i] = lines[i].Trim();
        }

        // 重新组合，移除空行
        content = string.Join('\n', lines.Where(line => !string.IsNullOrWhiteSpace(line)));

        return content.Trim();
    }
}