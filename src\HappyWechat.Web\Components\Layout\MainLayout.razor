﻿@using HappyWechat.Infrastructure.Identity
@using HappyWechat.Web.Services
@rendermode @(new InteractiveServerRenderMode(prerender: false))
@inherits LayoutComponentBase
@implements IDisposable
@inject AuthenticationStateProvider AuthenticationStateProvider;
@inject NavigationManager Navigation;
@inject SystemConfigStateService SystemConfigStateService;
<MudThemeProvider Theme="@_theme" IsDarkMode="_isDarkMode"/>
<MudPopoverProvider/>
<MudDialogProvider/>
<MudSnackbarProvider/>

<AuthorizeView>
    <Authorized>
        <div class="layout-container" data-theme="@(_isDarkMode ? "dark" : "light")"
             style="@(_isDarkMode ?
               "--sidebar-bg: linear-gradient(135deg, #2d2d2d 0%, #1a1a27 100%); --sidebar-text: #b2b0bf; --main-bg: #1a1a27; --navbar-bg: #2d2d2d;" :
               "--sidebar-bg: linear-gradient(135deg, #4CAF50 0%, #2196F3 100%); --sidebar-text: white; --main-bg: #f5f5f5; --navbar-bg: white;")">
            <!-- 左侧菜单栏 -->
            <div class="sidebar @(_isDarkMode ? "sidebar-dark" : "")">
                <!-- Logo和公司信息区域 -->
                <div class="sidebar-header">
                    <MudImage Src="images/lexve.png" Alt="乐学AI" Width="32" Height="32" ObjectFit="ObjectFit.Cover"/>
                    <MudText Typo="Typo.h6" Class="ml-3 sidebar-title">@_systemTitle</MudText>
                </div>
                <!-- 菜单区域 -->
                <div class="sidebar-menu">
                    <MyNavMenu/>
                </div>
            </div>

            <!-- 右侧内容区域 -->
            <div class="main-area @(_isDarkMode ? "main-area-dark" : "")">
                <!-- 顶部导航栏 -->
                <div class="top-navbar">
                    <div class="navbar-left">
                        <MudBreadcrumbs Items="_breadcrumbItems" Class="breadcrumb-nav">
                            <ItemTemplate Context="item">
                                @if (item.Href != null)
                                {
                                    <MudLink Href="@item.Href" Class="breadcrumb-link">@item.Text</MudLink>
                                }
                                else
                                {
                                    <MudText Class="breadcrumb-current">@item.Text</MudText>
                                }
                            </ItemTemplate>
                        </MudBreadcrumbs>
                    </div>
                    <div class="navbar-right">
                        <MudIconButton Icon="@(DarkLightModeButtonIcon)" Color="Color.Inherit"
                                       OnClick="@DarkModeToggle" Class="theme-toggle"/>
                        <MudMenu Icon="@Icons.Material.Filled.MoreVert" Color="Color.Inherit">
                            <MudMenuItem Icon="@Icons.Material.Filled.Logout" IconColor="Color.Inherit"
                                         OnClick="HandleLogout">
                                登出
                            </MudMenuItem>
                        </MudMenu>
                    </div>
                </div>

                <!-- 页面内容区域 -->
                <div class="content-area">
                    @Body
                </div>
            </div>
        </div>

        <!-- 认证辅助组件 - 为SignalR提供JWT token访问 -->
        <HappyWechat.Web.Components.Auth.AuthHelper />
    </Authorized>
    <NotAuthorized>
        You are not authorized.
    </NotAuthorized>
</AuthorizeView>


<div id="blazor-error-ui" data-nosnippet>
    An unhandled error has occurred.
    <a href="." class="reload">Reload</a>
    <span class="dismiss">🗙</span>
</div>

@code {
    private bool _isDarkMode = false;
    private MudTheme? _theme = null;
    private List<BreadcrumbItem> _breadcrumbItems = new();
    private string _systemTitle = "乐学机器人 v1.0";

    protected override async Task OnInitializedAsync()
    {
        base.OnInitialized();

        _theme = new()
        {
            PaletteLight = _lightPalette,
            PaletteDark = _darkPalette,
            LayoutProperties = new LayoutProperties()
        };

        // 监听导航变化以更新面包屑
        Navigation.LocationChanged += OnLocationChanged;
        UpdateBreadcrumbs();

        // 监听系统配置变化
        SystemConfigStateService.SystemInfoChanged += OnSystemInfoChanged;

        // 加载系统标题
        await LoadSystemTitle();
        
        // 强制初始化主题状态
        StateHasChanged();
    }

    private async Task LoadSystemTitle()
    {
        try
        {
            _systemTitle = await SystemConfigStateService.GetSystemTitleAsync();
            StateHasChanged();
        }
        catch
        {
            // 如果加载失败，保持默认值
        }
    }

    private async void OnSystemInfoChanged()
    {
        await LoadSystemTitle();
    }

    private void DarkModeToggle()
    {
        _isDarkMode = !_isDarkMode;
        StateHasChanged();
    }

    private void OnLocationChanged(object? sender, LocationChangedEventArgs e)
    {
        UpdateBreadcrumbs();
        InvokeAsync(StateHasChanged);
    }

    private void UpdateBreadcrumbs()
    {
        var uri = new Uri(Navigation.Uri);
        var path = uri.AbsolutePath;

        _breadcrumbItems.Clear();
        _breadcrumbItems.Add(new BreadcrumbItem("首页", href: "/home", icon: Icons.Material.Filled.Home));

        // 根据当前路径生成面包屑
        var menuItems = GetMenuItems();
        var currentMenu = menuItems.FirstOrDefault(m => m.Href == path);

        if (currentMenu != null && path != "/home")
        {
            _breadcrumbItems.Add(new BreadcrumbItem(currentMenu.Title, href: null));
        }
    }

    private List<MenuItem> GetMenuItems()
    {
        return new List<MenuItem>
        {
            new() { Href = "/home", Title = "首页" },
            new() { Href = "/wxManage", Title = "微信管理" },
            new() { Href = "/wxContact", Title = "联系人管理" },
            new() { Href = "/wxGroup", Title = "群聊管理" },
            new() { Href = "/friendCircle", Title = "朋友圈管理" },
            new() { Href = "/videoAccount", Title = "视频号管理" },
            new() { Href = "/jobManage", Title = "定时任务" },
            new() { Href = "/tagManage", Title = "标签管理" },
            new() { Href = "/materialManage", Title = "素材管理" },
            new() { Href = "/ai-manage", Title = "AI配置" },
            new() { Href = "/system-config", Title = "系统配置" }
        };
    }

    public class MenuItem
    {
        public string Href { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
    }

    private readonly PaletteLight _lightPalette = new()
    {
        Black = "#110e2d",
        AppbarText = "#424242",
        AppbarBackground = "rgba(76, 175, 80, 1.0)",
        DrawerBackground = "rgba(76, 175, 80, 1.0)",
        GrayLight = "#e8e8e8",
        GrayLighter = "#f9f9f9",
        DrawerText = "#E6E6FA",
        DrawerIcon = "#E6E6FA",
        Secondary = "#3CB371",
    };

    private readonly PaletteDark _darkPalette = new()
    {
        Primary = "#7e6fff",
        Surface = "#1e1e2d",
        Background = "#1a1a27",
        BackgroundGray = "#151521",
        AppbarText = "#92929f",
        AppbarBackground = "rgba(26,26,39,0.8)",
        DrawerBackground = "#1a1a27",
        ActionDefault = "#74718e",
        ActionDisabled = "#9999994d",
        ActionDisabledBackground = "#605f6d4d",
        TextPrimary = "#b2b0bf",
        TextSecondary = "#92929f",
        TextDisabled = "#ffffff33",
        DrawerIcon = "#7e6fff",
        DrawerText = "#7e6fff",
        GrayLight = "#2a2833",
        GrayLighter = "#1e1e2d",
        Info = "#4a86ff",
        Success = "#3dcb6c",
        Warning = "#ffb545",
        Error = "#ff3f5f",
        LinesDefault = "#33323e",
        TableLines = "#33323e",
        Divider = "#292838",
        OverlayLight = "#1e1e2d80",
        // 导航选中状态颜色保持与DrawerIcon和DrawerText一致
        Secondary = "#7e6fff",
    };

    public string DarkLightModeButtonIcon => _isDarkMode switch
    {
        true => Icons.Material.Outlined.DarkMode,
        false => Icons.Material.Rounded.LightMode,
    };

    public async Task HandleLogout()
    {
        if (AuthenticationStateProvider is HappyWechat.Infrastructure.Auth.RedisAuthenticationStateProvider redisProvider)
        {
            await redisProvider.MarkUserAsLoggedOutAsync();
        }
        Navigation.NavigateTo("/login");
    }

    public void Dispose()
    {
        Navigation.LocationChanged -= OnLocationChanged;
        SystemConfigStateService.SystemInfoChanged -= OnSystemInfoChanged;
    }

}

<script>
    // 🚀 配置变更实时通知系统
    window.sendConfigurationChangeNotification = function(wxManagerId, configType, changeData) {
        try {
            console.log('📢 发送配置变更通知:', { wxManagerId, configType, changeData });

            // 发送自定义事件，其他页面可以监听
            const event = new CustomEvent('configurationChanged', {
                detail: { wxManagerId, configType, changeData }
            });
            window.dispatchEvent(event);

            // 🔧 修复：强制刷新页面缓存
            if (window.DotNet && window.DotNet.invokeMethodAsync) {
                window.DotNet.invokeMethodAsync('HappyWechat.Web', 'RefreshConfigurationCache', wxManagerId, configType)
                    .catch(err => console.warn('刷新配置缓存失败:', err));
            }

            // 如果有SignalR连接，也可以通过SignalR广播
            if (window.signalRConnection && window.signalRConnection.state === 'Connected') {
                window.signalRConnection.invoke('BroadcastConfigurationChange', wxManagerId, configType, changeData)
                    .catch(err => console.warn('SignalR配置变更通知失败:', err));
            }

            // 🔧 修复：延迟刷新页面以确保配置生效
            setTimeout(() => {
                console.log('🔄 配置变更通知完成，建议刷新页面以确保配置生效');
            }, 1000);

            return true;
        } catch (error) {
            console.error('发送配置变更通知失败:', error);
            return false;
        }
    };

    window.sendGlobalConfigurationChangeNotification = function(configType, changeData) {
        try {
            console.log('📢 发送全局配置变更通知:', { configType, changeData });

            // 发送全局配置变更事件
            const event = new CustomEvent('globalConfigurationChanged', {
                detail: { configType, changeData }
            });
            window.dispatchEvent(event);

            // 🔧 修复：强制刷新全局配置缓存
            if (window.DotNet && window.DotNet.invokeMethodAsync) {
                window.DotNet.invokeMethodAsync('HappyWechat.Web', 'RefreshGlobalConfigurationCache', configType)
                    .catch(err => console.warn('刷新全局配置缓存失败:', err));
            }

            // 如果有SignalR连接，也可以通过SignalR广播
            if (window.signalRConnection && window.signalRConnection.state === 'Connected') {
                window.signalRConnection.invoke('BroadcastGlobalConfigurationChange', configType, changeData)
                    .catch(err => console.warn('SignalR全局配置变更通知失败:', err));
            }

            // 🔧 修复：延迟刷新页面以确保全局配置生效
            setTimeout(() => {
                console.log('🔄 全局配置变更通知完成，建议刷新页面以确保配置生效');
            }, 1000);

            return true;
        } catch (error) {
            console.error('发送全局配置变更通知失败:', error);
            return false;
        }
    };

    // 监听配置变更事件，自动刷新页面缓存
    window.addEventListener('configurationChanged', function(event) {
        console.log('🔄 收到配置变更通知，刷新相关缓存:', event.detail);
        // 这里可以添加具体的缓存刷新逻辑
    });

    window.addEventListener('globalConfigurationChanged', function(event) {
        console.log('🔄 收到全局配置变更通知，刷新全局缓存:', event.detail);
        // 这里可以添加具体的全局缓存刷新逻辑
    });
</script>

