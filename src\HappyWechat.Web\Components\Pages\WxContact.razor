﻿@page "/wxContact"
@attribute [Authorize]
@using Microsoft.AspNetCore.Authorization
@using System.Collections
@using System.Collections.Specialized
@using Humanizer
@using HappyWechat.Application.Commons
@using HappyWechat.Application.DTOs.Requests.Queries
@using HappyWechat.Application.DTOs.Requests.Commands
@using HappyWechat.Application.DTOs.Responses
@using HappyWechat.Domain.ValueObjects.Enums
@using HappyWechat.Web.Components.Wx
@using HappyWechat.Web.FrontApis
@using HappyWechat.Web.VOs
@using HappyWechat.Application.DTOs.AiAgent
@using HappyWechat.Application.DTOs.AiConfig
@using HappyWechat.Web.Components.Common
@using HappyWechat.Web.Services
@using HappyWechat.Application.Interfaces
@using System.Net.Http.Json
@using HappyWechat.Web.Services.Interfaces
@using HappyWechat.Web.Services.Models
@using HappyWechat.Web.Constants
@using HappyWechat.Infrastructure.Services
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components.Authorization
@using System.Text.Json
@using HappyWechat.Infrastructure.Configuration
@using HappyWechat.Infrastructure.MessageProcessing
@inherits BaseWxPageComponent
@inject IUnifiedConfigManager UnifiedConfigManager
@inject IConfigurationEffectivenessTracker ConfigurationEffectivenessTracker
@inject IConfigurationConsistencyService ConfigurationConsistencyService
@inject HappyWechat.Infrastructure.Caching.Interfaces.IUnifiedCacheManager UnifiedCacheManager
@inject HappyWechat.Web.Services.Interfaces.IUnifiedDataManager UnifiedDataManager
@inject HappyWechat.Web.Services.Interfaces.IUnifiedFrontendCacheService UnifiedFrontendCacheService
@inject HappyWechat.Web.Services.Interfaces.ISimplifiedDataRefreshService SimplifiedDataRefreshService
@inject HappyWechat.Web.Services.Interfaces.ISimplifiedSyncStateManager SimplifiedSyncStateManager
@inject IUnifiedConfigService UnifiedConfigService
@inject HttpClient HttpClient
@inject HappyWechat.Web.Services.ISearchStateService SearchStateService
@inject HappyWechat.Web.Services.Interfaces.IDataSyncManager DataSyncManager
@inject HappyWechat.Web.Services.ISearchDebounceService SearchDebounceService
@inject HappyWechat.Infrastructure.Wx.IWxContactQueryOptimizer ContactQueryOptimizer
@inject HappyWechat.Infrastructure.DataSync.IDataSyncOrchestrator DataSyncOrchestrator
<PageTitle>微信联系人管理</PageTitle>

<style>
    .skeleton-table-container {
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        overflow: hidden;
    }

    .skeleton-table-header {
        display: flex;
        background: #f5f5f5;
        border-bottom: 1px solid #e0e0e0;
        font-size: 14px;
        font-weight: 500;
        align-items: center;
        padding: 8px 0;
    }

    .skeleton-table-header div {
        padding: 8px 12px;
        text-align: left;
        font-size: 14px;
        color: #666;
        display: flex;
        align-items: center;
    }

    .search-input-enhanced {
        position: relative;
    }

    .search-input-enhanced .search-status {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;
    }
</style>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-0">
    @if (!_hasWeChatAccounts)
    {
        <MudAlert Severity="Severity.Warning" Class="ma-4">
            <MudText Typo="Typo.h6">需要先设置微信账户</MudText>
            <MudText Class="mt-2">您还没有任何微信账户。请先前往微信管理页面添加您的微信账户。</MudText>
            <MudButton Variant="Variant.Filled" Color="Color.Primary" Class="mt-2" OnClick="NavigateToWxManage">
                前往微信管理
            </MudButton>
        </MudAlert>
    }
    else if (!_hasLoggedInAccounts)
    {
        <MudAlert Severity="Severity.Info" Class="ma-4">
            <MudText Typo="Typo.h6">微信账户未登录</MudText>
            <MudText Class="mt-2">您的微信账户都未登录。请前往微信管理页面登录您的微信账户。</MudText>
            <MudButton Variant="Variant.Filled" Color="Color.Primary" Class="mt-2" OnClick="NavigateToWxManage">
                前往微信管理
            </MudButton>
        </MudAlert>
    }
    else if (!_hasEnabledAccounts)
    {
        <MudAlert Severity="Severity.Warning" Class="ma-4">
            <MudText Typo="Typo.h6">微信账户已被禁用</MudText>
            <MudText Class="mt-2">您的微信账户都已被禁用。请前往微信管理页面启用您的微信账户，或联系管理员。</MudText>
            <MudButton Variant="Variant.Filled" Color="Color.Primary" Class="mt-2" OnClick="NavigateToWxManage">
                前往微信管理
            </MudButton>
        </MudAlert>
    }
    else
    {
        <!-- 整体卡片容器 -->
        <MudCard Elevation="1" Class="ma-0">
            <!-- 微信账号Tab选择和操作栏 -->
            <MudCardContent Class="pa-0">
                <!-- 顶部标签页区域 -->
                <MudPaper Elevation="0" Class="border-bottom">
                    <MudTabs ActivePanelIndex="_activeTabIndex"
                             Elevation="0"
                             PanelClass="pa-0"
                             HeaderClass="mb-0"
                             Class="mud-width-full"
                             ActivePanelIndexChanged="@((int index) => OnTabChanged(index))">
                        @if (wxAccounts != null && wxAccounts.Any())
                        {
                            @foreach (var account in wxAccounts.Select((account, index) => new { account, index }))
                            {
                                <MudTabPanel>
                                    <TabContent>
                                        <MudStack AlignItems="AlignItems.Center" Row="true" Spacing="1" Class="tab-content-40px">
                                            <!-- 左侧头像 -->
                                            <MudAvatar Size="Size.Medium">
                                                @if (!string.IsNullOrEmpty(account.account.HeadUrl))
                                                {
                                                    <MudImage Src="@account.account.HeadUrl" />
                                                }
                                                else
                                                {
                                                    <MudIcon Icon="Icons.Material.Filled.Person" />
                                                }
                                            </MudAvatar>
                                            <!-- 右侧信息区域 -->
                                            <MudStack Row="false" Spacing="0" Justify="Justify.Center" AlignItems="AlignItems.Start">
                                                <!-- 上行：微信昵称 -->
                                                <MudText Typo="Typo.body2" Class="mb-0" Color="Color.Primary">@account.account.NickName</MudText>
                                                <!-- 下行：在线状态 -->
                                                <MudChip T="string"
                                                         Color="@GetStatusColor(account.account.WxStatus)"
                                                         Size="Size.Small"
                                                         Class="mt-1">
                                                    @GetStatusText(account.account.WxStatus)
                                                </MudChip>
                                            </MudStack>
                                        </MudStack>
                                    </TabContent>
                                    <ChildContent>
                                        <!-- 操作栏 -->
                                        <MudPaper Elevation="0" Class="operation-toolbar">
                                            <MudGrid AlignItems="AlignItems.Center" Spacing="1" Class="align-center">
                                                <!-- 第一行：搜索和添加联系人 -->
                                                <!-- 搜索联系人 -->
                                                <MudItem xs="12" sm="6" md="2">
                                                    <div class="search-input-enhanced">
                                                        <MudTextField @bind-Value="_searchedNickName"
                                                                      @oninput="OnSearchInput"
                                                                      Variant="Variant.Outlined"
                                                                      Adornment="Adornment.Start"
                                                                      AdornmentIcon="Icons.Material.Filled.Search"
                                                                      Placeholder="输入昵称搜索 (防抖动)"
                                                                      Dense="true"
                                                                      Class="@(_isSearching ? "searching" : "")"
                                                                      Disabled="_isSearching"
                                                                      Immediate="false" />
                                                        <div class="search-status">
                                                            @if (_isSearching)
                                                            {
                                                                <MudProgressCircular Size="Size.Small" Color="Color.Primary" Indeterminate="true" />
                                                            }
                                                            else if (!string.IsNullOrEmpty(_searchedNickName) && Elements.Any())
                                                            {
                                                                <MudIcon Icon="Icons.Material.Filled.Check" Color="Color.Success" Size="Size.Small" />
                                                            }
                                                        </div>
                                                    </div>
                                                </MudItem>

                                                <!-- 搜索按钮 -->
                                                <MudItem xs="12" sm="6" md="1">
                                                    <MudButton Variant="Variant.Outlined"
                                                               Color="Color.Primary"
                                                               OnClick="() => SearchContacts()"
                                                               Size="Size.Small"
                                                               Disabled="@(_isSyncing || _isLoading)"
                                                               FullWidth="true">
                                                        @if (_isSearching)
                                                        {
                                                            <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                                            <span class="ml-1">搜索中</span>
                                                        }
                                                        else
                                                        {
                                                            <span>搜索</span>
                                                        }
                                                    </MudButton>
                                                </MudItem>

                                                <!-- 添加联系人 -->
                                                <MudItem xs="12" sm="6" md="3">
                                                    <MudTextField @bind-Value="manualContactId"
                                                                  @onkeypress="OnContactIdKeyPress"
                                                                  Variant="Variant.Outlined"
                                                                  Adornment="Adornment.Start"
                                                                  AdornmentIcon="Icons.Material.Filled.PersonAdd"
                                                                  Placeholder="请输入wcid、微信ID添加联系人"
                                                                  HelperText="支持wcid、微信ID、手机号等格式"
                                                                  Immediate="true"
                                                                  Validation="@(new Func<string, string?>(ValidateContactId))"
                                                                  Dense="true" />
                                                </MudItem>

                                                <MudItem xs="6" sm="3" md="1" Class="d-flex align-center">
                                                    <MudButton Variant="Variant.Filled"
                                                               Color="Color.Primary"
                                                               Size="Size.Small"
                                                               OnClick="AddContactManually"
                                                               Disabled="@(string.IsNullOrEmpty(manualContactId?.Trim()) || !HasSelectedAccount || _isAddingContact || _isSyncing)"
                                                               StartIcon="@(_isAddingContact ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.PersonAdd)"
                                                               FullWidth="true">
                                                        @if (_isAddingContact)
                                                        {
                                                            <span>添加中</span>
                                                        }
                                                        else
                                                        {
                                                            <span>添加</span>
                                                        }
                                                    </MudButton>
                                                </MudItem>

                                                <!-- 右侧操作按钮 -->
                                                <MudItem xs="12" md="5" Class="d-flex justify-end align-center">
                                                    <MudStack Row="true" Spacing="2" AlignItems="AlignItems.Center">
                                                        <!-- 选中状态显示 -->
                                                        @if (_selectedContactIds.Count > 0)
                                                        {
                                                            <MudChip T="string" Size="Size.Small" Color="Color.Primary">
                                                                已选中 @_selectedContactIds.Count 个联系人
                                                            </MudChip>
                                                            <MudButton Variant="Variant.Text"
                                                                       Color="Color.Warning"
                                                                       Size="Size.Small"
                                                                       OnClick="() => ClearSelection()">
                                                                清空选择
                                                            </MudButton>
                                                        }

                                                        <MudButton Variant="Variant.Filled"
                                                                   Color="Color.Success"
                                                                   Size="Size.Medium"
                                                                   OnClick="GetContactsForCurrentAccount"
                                                                   Disabled="@(_isSyncing || !HasSelectedAccount || _isLoading)">
                                                            @if (_isSyncing)
                                                            {
                                                                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                                                <span class="ml-2">获取中...</span>
                                                            }
                                                            else
                                                            {
                                                                <MudIcon Icon="@Icons.Material.Filled.Download" />
                                                                <span class="ml-2">获取联系人</span>
                                                            }
                                                        </MudButton>

                                                        <MudButton Variant="Variant.Outlined"
                                                                   Color="Color.Secondary"
                                                                   Size="Size.Medium"
                                                                   OnClick="ShowBatchEditDialog"
                                                                   Disabled="@(_selectedContactIds.Count == 0 || _deletingContactIds.Count > 0 || _isBatchDeleting)">
                                                            @if (_isBatchDeleting)
                                                            {
                                                                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                                                <span class="ml-1">批量处理中...</span>
                                                            }
                                                            else
                                                            {
                                                                <span>批量编辑 (@_selectedContactIds.Count)</span>
                                                            }
                                                        </MudButton>

                                                        <!-- 🔧 调试和故障恢复按钮 -->
                                                        <MudMenu Icon="Icons.Material.Filled.Settings"
                                                                 Color="Color.Default"
                                                                 Size="Size.Medium"
                                                                 Variant="Variant.Outlined">
                                                            <MudMenuItem OnClick="ForceReloadData">
                                                                <MudIcon Icon="Icons.Material.Filled.Refresh" />
                                                                <span class="ml-2">强制重新加载</span>
                                                            </MudMenuItem>
                                                            <MudMenuItem OnClick="CheckDataStatus">
                                                                <MudIcon Icon="Icons.Material.Filled.Info" />
                                                                <span class="ml-2">检查数据状态</span>
                                                            </MudMenuItem>
                                                        </MudMenu>
                                                    </MudStack>
                                                </MudItem>
                                            </MudGrid>
                                        </MudPaper>

                                        <!-- 🚀 简化：删除复杂的进度显示，使用简单的按钮状态 -->

                                        <!-- 搜索状态提示 -->
                                        @if (_isSearching)
                                        {
                                            <MudPaper Elevation="0" Class="pa-2">
                                                <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="2">
                                                    <MudProgressCircular Color="Color.Primary" Size="Size.Small" Indeterminate="true" />
                                                    <MudText Typo="Typo.body2" Class="mud-text-secondary">正在搜索联系人...</MudText>
                                                </MudStack>
                                            </MudPaper>
                                        }

                                        <!-- 联系人列表 -->
                                        <MudPaper Elevation="0" Class="pa-0">
                                            @if (_isLoading || _isSearching)
                                            {
                                                <!-- 骨架屏加载 -->
                                                <div class="skeleton-table-container">
                                                    <div class="skeleton-table-header">
                                                        <div style="width: 50px; height: 40px; background: #f5f5f5; border-right: 1px solid #e0e0e0;"></div>
                                                        <div style="width: 80px; height: 40px; background: #f5f5f5; border-right: 1px solid #e0e0e0;">头像</div>
                                                        <div style="width: 140px; height: 40px; background: #f5f5f5; border-right: 1px solid #e0e0e0;">微信号</div>
                                                        <div style="width: 160px; height: 40px; background: #f5f5f5; border-right: 1px solid #e0e0e0;">昵称</div>
                                                        <div style="width: 100px; height: 40px; background: #f5f5f5; border-right: 1px solid #e0e0e0;">省份</div>
                                                        <div style="width: 120px; height: 40px; background: #f5f5f5; border-right: 1px solid #e0e0e0;">备注</div>
                                                        <div style="width: 80px; height: 40px; background: #f5f5f5; border-right: 1px solid #e0e0e0;">性别</div>
                                                        <div style="width: 200px; height: 40px; background: #f5f5f5; border-right: 1px solid #e0e0e0;">机器人配置</div>
                                                        <div style="width: 120px; height: 40px; background: #f5f5f5; border-right: 1px solid #e0e0e0;">模式</div>
                                                        <div style="width: 160px; height: 40px; background: #f5f5f5;">操作</div>
                                                    </div>
                                                    <SkeletonLoader Type="SkeletonLoader.SkeletonType.DataTable" Count="@(_pageSize)" />
                                                </div>
                                            }
                                            else
                                            {
                                                <MudTable Items="@Elements"
                                                      Hover="true"
                                                      Striped="true"
                                                      Loading="false"
                                                      Dense="true"
                                                      Class="compact-table fixed-layout-table">
                                            <HeaderContent>
                                                <MudTh Style="width: 50px;">
                                                    <MudCheckBox @bind-Value="_isAllSelected"
                                                                 Indeterminate="_isIndeterminate"
                                                                 TriState="true"
                                                                 Size="Size.Small" />
                                                </MudTh>
                                                <MudTh Style="width: 80px;">头像</MudTh>
                                                <MudTh Style="width: 140px;">微信号</MudTh>
                                                <MudTh Style="width: 160px;">昵称</MudTh>
                                                <MudTh Style="width: 100px;">省份</MudTh>
                                                <MudTh Style="width: 120px;">备注</MudTh>
                                                <MudTh Style="width: 80px;">性别</MudTh>
                                                <MudTh Style="width: 200px;">机器人配置</MudTh>
                                                <MudTh Style="width: 120px;">模式</MudTh>
                                                <MudTh Style="width: 160px;">操作</MudTh>
                                            </HeaderContent>
                                            <RowTemplate>
                                                <MudTd Class="compact-row">
                                                    <MudCheckBox Value="@_selectedContactIds.Contains(context.Id)"
                                                                 ValueChanged="@((bool value) => OnContactSelectionChanged(context.Id, value))"
                                                                 Size="Size.Small" />
                                                </MudTd>
                                                <MudTd Class="compact-row">
                                                    <MudAvatar Size="Size.Medium">
                                                        @if (!string.IsNullOrEmpty(context.SmallHead))
                                                        {
                                                            <MudImage Src="@context.SmallHead" />
                                                        }
                                                        else
                                                        {
                                                            <MudIcon Icon="Icons.Material.Filled.Person" />
                                                        }
                                                    </MudAvatar>
                                                </MudTd>
                                                <MudTd Class="compact-row"><MudText Typo="Typo.body2">@context.WcId</MudText></MudTd>
                                                <MudTd Class="compact-row">
                                                    <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="1">
                                                        <MudText Typo="Typo.body2">@context.NickName</MudText>
                                                        @if (context.ContactType == WxContactType.Enterprise)
                                                        {
                                                            <MudChip T="string" Size="Size.Small" Color="Color.Info" Class="compact-chip">
                                                                企业
                                                            </MudChip>
                                                        }
                                                        else if (context.ContactType == WxContactType.Contact)
                                                        {
                                                            <MudChip T="string" Size="Size.Small" Color="Color.Primary" Class="compact-chip">
                                                                个人
                                                            </MudChip>
                                                        }
                                                    </MudStack>
                                                </MudTd>
                                                <MudTd Class="compact-row"><MudText Typo="Typo.body2">@context.Province</MudText></MudTd>
                                                <MudTd Class="compact-row"><MudText Typo="Typo.body2">@context.Remark</MudText></MudTd>
                                                <MudTd Class="compact-row"><MudText Typo="Typo.body2">@(context.Sex == 1 ? "男" : context.Sex == 2 ? "女" : "未知")</MudText></MudTd>
                                                <MudTd Class="compact-row">
                                                    <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="1">
                                                        <MudSelect T="string" Value="@GetSelectedAiAgentName(context)"
                                                                   ValueChanged="@((string value) => OnAiAgentChanged(context, value))"
                                                                   Dense="true" Variant="Variant.Outlined" Placeholder="选择AI机器人"
                                                                   Class="compact-select" Style="min-width: 120px;">
                                                            <MudSelectItem Value="@("")">
                                                                <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="1">
                                                                    <MudIcon Icon="Icons.Material.Filled.Clear" Size="Size.Small" Color="Color.Error" />
                                                                    <MudText>未配置</MudText>
                                                                </MudStack>
                                                            </MudSelectItem>
                                                            @if (aiAgents != null)
                                                            {
                                                                @foreach (var agent in aiAgents.Where(a => a.IsEnabled))
                                                                {
                                                                    <MudSelectItem Value="@agent.Name">
                                                                        <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="1">
                                                                            <MudIcon Icon="Icons.Material.Filled.SmartToy" Size="Size.Small" Color="Color.Primary" />
                                                                            <MudText>@agent.Name</MudText>
                                                                        </MudStack>
                                                                    </MudSelectItem>
                                                                }
                                                            }
                                                        </MudSelect>

                                                        @* 配置状态指示器 *@
                                                        @{
                                                            var hasAiAgent = contactAiAgentMapping.ContainsKey(context.Id);
                                                            var agentName = hasAiAgent ? contactAiAgentMapping[context.Id] : "";
                                                            var isAutoReply = context.AutoReplyEnabled;
                                                        }

                                                        @if (hasAiAgent && isAutoReply)
                                                        {
                                                            <MudTooltip Text="@($"已配置AI机器人: {agentName} (自动回复)")">
                                                                <MudIcon Icon="Icons.Material.Filled.CheckCircle" Size="Size.Small" Color="Color.Success" />
                                                            </MudTooltip>
                                                        }
                                                        else if (hasAiAgent && !isAutoReply)
                                                        {
                                                            <MudTooltip Text="@($"已配置AI机器人: {agentName} (人工回复)")">
                                                                <MudIcon Icon="Icons.Material.Filled.SmartToy" Size="Size.Small" Color="Color.Info" />
                                                            </MudTooltip>
                                                        }
                                                        else if (isAutoReply)
                                                        {
                                                            <MudTooltip Text="已启用自动回复但未配置AI机器人">
                                                                <MudIcon Icon="Icons.Material.Filled.Warning" Size="Size.Small" Color="Color.Warning" />
                                                            </MudTooltip>
                                                        }
                                                        else
                                                        {
                                                            <MudTooltip Text="未配置AI机器人 (人工回复)">
                                                                <MudIcon Icon="Icons.Material.Filled.Person" Size="Size.Small" Color="Color.Default" />
                                                            </MudTooltip>
                                                        }
                                                    </MudStack>
                                                </MudTd>
                                                <MudTd Class="compact-row">
                                                    <MudSelect T="string" Value="@(GetContactMode(context))"
                                                               ValueChanged="@((string value) => OnModeChanged(context, value))"
                                                               Dense="true" Variant="Variant.Outlined"
                                                               Class="compact-select">
                                                        <MudSelectItem Value="@("自动回复")">
                                                            <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="1">
                                                                <MudIcon Icon="Icons.Material.Filled.AutoMode" Size="Size.Small" Color="Color.Success" />
                                                                <MudText>自动回复</MudText>
                                                            </MudStack>
                                                        </MudSelectItem>
                                                        <MudSelectItem Value="@("人工回复")">
                                                            <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="1">
                                                                <MudIcon Icon="Icons.Material.Filled.Person" Size="Size.Small" Color="Color.Warning" />
                                                                <MudText>人工回复</MudText>
                                                            </MudStack>
                                                        </MudSelectItem>
                                                    </MudSelect>
                                                </MudTd>
                                                <MudTd Class="compact-row">
                                                    <MudStack Row="true" Spacing="1">

                                                        <MudButton Variant="Variant.Text"
                                                                   Color="Color.Error"
                                                                   Size="Size.Small"
                                                                   OnClick="() => DeleteContact(context)"
                                                                   Disabled="@(_deletingContactIds.Contains(context.Id) || _isBatchDeleting)">
                                                            @if (_deletingContactIds.Contains(context.Id))
                                                            {
                                                                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                                                <span class="ml-1">删除中</span>
                                                            }
                                                            else
                                                            {
                                                                <span>删除</span>
                                                            }
                                                        </MudButton>
                                                        <MudButton Variant="Variant.Text"
                                                                   Color="Color.Default"
                                                                   Size="Size.Small"
                                                                   OnClick="() => SendMessage(context)">
                                                            私信
                                                        </MudButton>
                                                    </MudStack>
                                                </MudTd>
                                            </RowTemplate>
                                        </MudTable>
                                            }
                                        </MudPaper>

                                        <!-- 分页信息和分页控件 -->
                                        <MudPaper Elevation="0" Class="pa-2 border-top">
                                            <MudGrid AlignItems="AlignItems.Center">
                                                <MudItem xs="6">
                                                    <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="2">
                                                        <MudText Typo="Typo.body2" Class="mud-text-secondary">
                                                            显示 1 至 @Math.Min(_pageSize, Elements.Count()) 条, 共 @Elements.Count() 条
                                                        </MudText>
                                                        @if (_selectedContactIds.Count > 0)
                                                        {
                                                            <MudChip T="string" Size="Size.Small" Color="Color.Primary">
                                                                已选中: @_selectedContactIds.Count
                                                            </MudChip>
                                                        }
                                                        @if (!string.IsNullOrEmpty(_searchedNickName))
                                                        {
                                                            <MudChip T="string" Size="Size.Small" Color="Color.Info">
                                                                搜索: @_searchedNickName
                                                            </MudChip>
                                                        }
                                                    </MudStack>
                                                </MudItem>
                                                <MudItem xs="6" Class="d-flex justify-end">
                                                    @if (_totalPages > 1)
                                                    {
                                                        <MudPagination Count="@_totalPages"
                                                                       Selected="@_page"
                                                                       SelectedChanged="OnPageChanged"
                                                                       Size="Size.Small" />
                                                    }
                                                </MudItem>
                                            </MudGrid>
                                        </MudPaper>
                                    </ChildContent>
                                </MudTabPanel>
                            }
                        }
                    </MudTabs>
                </MudPaper>
            </MudCardContent>
        </MudCard>
    }
</MudContainer>



<style>
    .border-bottom {
        border-bottom: 1px solid var(--mud-palette-divider);
    }

    .border-top {
        border-top: 1px solid var(--mud-palette-divider);
    }

    .align-center {
        align-items: center;
    }

    /* 紧凑表格样式 - 重点控制行高 */
    .compact-table {
        --mud-table-row-height: 28px !important;
    }

    /* 固定表格布局样式 */
    .fixed-layout-table {
        table-layout: fixed !important;
        width: 100% !important;
    }

    .fixed-layout-table .mud-table {
        table-layout: fixed !important;
        width: 100% !important;
    }

    .compact-table .mud-table-row {
        height: 28px !important;
        min-height: 28px !important;
        max-height: 28px !important;
    }

    .compact-table .mud-table-cell {
        padding: 3px 8px !important;
        height: 28px !important;
        min-height: 28px !important;
        max-height: 28px !important;
        vertical-align: middle !important;
        line-height: 1.2 !important;
    }

    .compact-row {
        padding: 2px 6px !important;
        height: 28px !important;
        min-height: 28px !important;
        max-height: 28px !important;
        vertical-align: middle !important;
        line-height: 1.2 !important;
    }

    .compact-row .mud-typography {
        line-height: 1.2 !important;
        margin: 0 !important;
    }

    .compact-select {
        min-height: 24px !important;
        max-height: 24px !important;
    }

    .compact-select .mud-input {
        min-height: 24px !important;
        max-height: 24px !important;
        padding: 2px 8px !important;
        line-height: 1.2 !important;
    }

    .compact-select .mud-input-control {
        margin-top: 0 !important;
        margin-bottom: 0 !important;
    }

    .compact-select .mud-select {
        min-height: 24px !important;
        max-height: 24px !important;
    }

    /* 强制所有表格内容垂直居中且紧凑 */
    .compact-table td {
        padding: 2px 6px !important;
        height: 28px !important;
        vertical-align: middle !important;
    }

    /* 表格标题文字水平居中 */
    .compact-table .mud-table-head .mud-table-cell {
        text-align: center !important;
    }

    .compact-table .mud-avatar {
        margin: 0 !important;
    }

    .compact-table .mud-icon-button {
        margin: 0 !important;
        padding: 2px !important;
    }

    /* 紧凑操作栏样式 */
    .compact-toolbar {
        height: 28px !important;
        min-height: 28px !important;
        max-height: 28px !important;
        padding: 2px 8px !important;
        display: flex !important;
        align-items: center !important;
    }

    .compact-toolbar-grid {
        height: 24px !important;
        min-height: 24px !important;
    }

    .compact-toolbar .mud-grid {
        height: 24px !important;
        align-items: center !important;
    }

    .compact-toolbar .mud-grid-item {
        height: 24px !important;
        display: flex !important;
        align-items: center !important;
    }

    .compact-toolbar .mud-input {
        height: 24px !important;
        min-height: 24px !important;
    }

    .compact-toolbar .mud-input-control {
        height: 24px !important;
        min-height: 24px !important;
    }

    .compact-toolbar .mud-input-control .mud-input {
        height: 24px !important;
        min-height: 24px !important;
        padding: 2px 8px !important;
        font-size: 12px !important;
    }

    .compact-toolbar .mud-button {
        height: 24px !important;
        min-height: 24px !important;
        padding: 2px 8px !important;
        font-size: 12px !important;
    }

    .compact-toolbar .mud-button-root {
        height: 24px !important;
        min-height: 24px !important;
    }

    /* Tab区域样式优化 */
    .mud-tabs .mud-tab-panel {
        min-height: auto !important;
        max-height: none !important;
        overflow: visible !important;
        padding: 0 !important;
    }

    .mud-tabs .mud-tabs-panels {
        border-top: 1px solid #e0e0e0;
        background-color: #fafafa;
        padding: 0 !important;
        margin: 0 !important;
    }

    /* 确保Tab内容区域完全铺满 */
    .mud-tabs .mud-tabs-panels .mud-tab-panel {
        padding: 0 !important;
        margin: 0 !important;
    }

    /* 操作栏24px高度样式 */
    .operation-toolbar {
        height: 40px !important;
        min-height: 40px !important;
        max-height: 40px !important;
        padding: 0 8px !important;
        display: flex !important;
        align-items: center !important;
        overflow: hidden !important;
    }

    .operation-toolbar .mud-grid {
        height: 30px !important;
        min-height: 30px !important;
        align-items: center !important;
        margin: 0 !important;
    }

    .operation-toolbar .mud-grid-item {
        height: 30px !important;
        min-height: 30px !important;
        display: flex !important;
        align-items: center !important;
        padding: 0 2px !important;
    }

    .operation-toolbar .mud-input-control {
        height: 26px !important;
        min-height: 26px !important;
        margin: 0 !important;
    }

    .operation-toolbar .mud-input {
        height: 26px !important;
        min-height: 26px !important;
        padding: 1px 6px !important;
        font-size: 11px !important;
        line-height: 1.2 !important;
    }

    .operation-toolbar .mud-input-outlined .mud-input-outlined-border {
        border-width: 1px !important;
    }

    .operation-toolbar .mud-input-label {
        font-size: 10px !important;
        line-height: 1.2 !important;
    }

    .operation-toolbar .mud-button {
        height: 26px !important;
        min-height: 26px !important;
        padding: 1px 6px !important;
        font-size: 11px !important;
        line-height: 1.2 !important;
    }

    .operation-toolbar .mud-button-root {
        height: 26px !important;
        min-height: 26px !important;
    }

    .operation-toolbar .mud-icon {
        font-size: 14px !important;
    }

    .operation-toolbar .mud-progress-circular {
        width: 20px !important;
        height: 20px !important;
    }

    /* Tab按钮内容40px高度样式 */
    .tab-content-40px {
        height: 40px !important;
        min-height: 40px !important;
        max-height: 40px !important;
        padding: 4px 8px !important;
        display: flex !important;
        align-items: center !important;
        overflow: hidden !important;
    }

    .tab-content-40px .mud-avatar {
        width: 32px !important;
        height: 32px !important;
        min-width: 32px !important;
        min-height: 32px !important;
    }

    .tab-content-40px .mud-avatar .mud-avatar-img,
    .tab-content-40px .mud-avatar .mud-icon {
        width: 32px !important;
        height: 32px !important;
        font-size: 20px !important;
    }

    .tab-content-40px .mud-stack {
        height: 32px !important;
        min-height: 32px !important;
        justify-content: center !important;
    }

    .tab-content-40px .mud-text {
        font-size: 12px !important;
        line-height: 1.2 !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .tab-content-40px .mud-chip {
        height: 16px !important;
        min-height: 16px !important;
        font-size: 10px !important;
        padding: 0 4px !important;
        margin: 1px 0 !important;
    }

    .tab-content-40px .mud-chip .mud-chip-content {
        padding: 0 !important;
        line-height: 1.2 !important;
    }

    /* MudTabPanel 圆角样式 */
    .mud-tabs {
        border-radius: 12px !important;
        overflow: hidden !important;
    }

    .mud-tabs .mud-tabs-toolbar {
        border-radius: 12px 12px 0 0 !important;
    }

    .mud-tabs .mud-tabs-panels {
        border-radius: 0 0 12px 12px !important;
    }

    .mud-tabs .mud-tab-panel {
        border-radius: 0 0 12px 12px !important;
    }

    /* 确保卡片容器也有圆角 */
    .mud-card {
        border-radius: 12px !important;
        overflow: hidden !important;
    }

    /* 特定组件不使用圆角 */
    .operation-toolbar {
        border-radius: 0 !important;
    }

    .mud-paper.pa-0 {
        border-radius: 0 !important;
    }

    .mud-paper.border-top {
        border-radius: 0 !important;
    }

    /* MudItem 内的组件不使用圆角 */
    .d-flex.justify-end.align-center .mud-paper {
        border-radius: 0 !important;
    }

    /* 按钮文字水平垂直居中 */
    .mud-button {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        text-align: center !important;
    }

    .mud-button .mud-button-label {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 100% !important;
    }

    .mud-button span {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* 搜索状态样式 */
    .searching {
        position: relative;
    }

    .searching::after {
        content: "";
        position: absolute;
        top: 50%;
        right: 8px;
        width: 12px;
        height: 12px;
        border: 2px solid #1976d2;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 0.8s linear infinite;
        transform: translateY(-50%);
    }

    /* 旋转动画 */
    @@keyframes spin {
        to {
            transform: translateY(-50%) rotate(360deg);
        }
    }

    /* 复选框样式 */
    .compact-table .mud-checkbox {
        margin: 0 !important;
        padding: 0 !important;
    }

    .compact-table .mud-checkbox .mud-button-root {
        min-height: 20px !important;
        min-width: 20px !important;
        padding: 0 !important;
    }

    .compact-table .mud-checkbox .mud-checkbox-icons {
        font-size: 16px !important;
    }

    /* 紧凑芯片样式 */
    .compact-chip {
        height: 16px !important;
        min-height: 16px !important;
        font-size: 10px !important;
        padding: 0 4px !important;
        margin: 0 !important;
    }

    .compact-chip .mud-chip-content {
        padding: 0 !important;
        line-height: 1.2 !important;
    }
</style>

@code {
    // 类型别名，避免泛型语法问题
    private static readonly Type TaskBoolType = typeof(Task<bool>);
    private static readonly Type ListObjectType = typeof(List<object>);
    private static readonly Type ListWxContactVoType = typeof(List<WxContactVo>);

    // 账户状态属性
    private bool _hasWeChatAccounts => wxAccounts?.Any() == true;
    private bool _hasLoggedInAccounts => wxAccounts?.Any(u => u.WxStatus == WxStatus.AlreadyLogIn) == true;
    private bool _hasEnabledAccounts => wxAccounts?.Any(u => u.WxStatus == WxStatus.AlreadyLogIn && u.IsEnabled) == true;

    // 联系人页面特有字段（基础字段已在基类中定义）
    private bool _hover = true;
    private WxContactVo? selectedItem;
    private string _searchedRemarkName = "";
    private string _searchedNickName = "";
    private IEnumerable<WxContactVo> Elements = new List<WxContactVo>();
    private int _page = 1;
    private int _pageSize = 10;
    private int _totalPages = 3;

    // 搜索相关字段
    private bool _isSearching = false;
    private string _lastSearchValue = "";
    private const string SEARCH_KEY = "contact_search";

    /// <summary>
    /// 搜索输入事件 - 防抖动处理
    /// </summary>
    private async Task OnSearchInput(ChangeEventArgs e)
    {
        var inputValue = e.Value?.ToString() ?? "";
        _searchedNickName = inputValue;

        // 设置搜索状态
        SearchStateService.SetSearching(SEARCH_KEY, true);

        // 如果输入为空，立即显示所有结果
        if (string.IsNullOrWhiteSpace(inputValue))
        {
            _lastSearchValue = inputValue;
            await SearchDebounceService.DebounceAsync(SEARCH_KEY, async () =>
            {
                SearchStateService.SetSearching(SEARCH_KEY, false);
                await ExecuteSearchImmediately();
                await InvokeAsync(StateHasChanged);
            }, 100); // 空搜索延迟短一些
            return;
        }

        // 防抖动搜索
        await SearchDebounceService.DebounceAsync(SEARCH_KEY, async () =>
        {
            // 验证搜索值是否仍然有效
            if (_searchedNickName == inputValue && _lastSearchValue != inputValue)
            {
                _lastSearchValue = inputValue;
                SearchStateService.SetSearching(SEARCH_KEY, false);
                await ExecuteSearchImmediately();
                await InvokeAsync(StateHasChanged);
            }
        }, 500); // 500ms 防抖动延迟
    }

    // 多选相关字段
    private HashSet<Guid> _selectedContactIds = new();
    private bool _isAllSelectedValue = false;
    private bool _isAllSelected
    {
        get => _isAllSelectedValue;
        set
        {
            if (_isAllSelectedValue != value)
            {
                _isAllSelectedValue = value;
                OnSelectAllChanged(value);
            }
        }
    }
    private bool _isIndeterminate = false;

    // 删除操作相关字段
    private HashSet<Guid> _deletingContactIds = new();
    private bool _isBatchDeleting = false;

    // 🚀 简化：联系人页面字段
    private Guid selectedAccountForManualAdd = Guid.Empty;
    private string manualContactId = "";
    private bool _isAddingContact = false;
    private WxContactListType manualContactType = WxContactListType.Friends;

    // 简单的同步状态
    private bool _isSyncing = false;
    private string? _refreshCallbackId;

    // API响应类型定义
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public T? Data { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }

    // AI机器人相关字段
    private Dictionary<Guid, string> contactAiAgentMapping = new(); // 联系人ID -> AI机器人名称
    private bool _isLoadingAiConfig = false; // AI配置加载状态
    private Dictionary<Guid, PageResponse<WxContactDto>> _tabDataCache = new(); // Tab数据缓存

    protected override async Task OnInitializedAsync()
    {
        // 调用基类的初始化方法（已包含所有基础初始化逻辑）
        await base.OnInitializedAsync();

        // 注册数据刷新回调
        _refreshCallbackId = UnifiedFrontendCacheService.RegisterDataRefreshCallback(
            "WxContact",
            OnDataRefreshCallback,
            null, // 响应所有微信管理器
            new List<string> { "Contact", "All" }
        );
    }

    /// <summary>
    /// 在DOM完全渲染后设置组件引用，确保JavaScript环境就绪
    /// </summary>
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);

        if (firstRender)
        {
            // 初始化SignalR连接
            await InitializeSignalRAsync();

            // 🔧 注册统一通知处理器回调
            await RegisterUnifiedNotificationCallbacks();

            // 注册简化刷新回调
            await RegisterSimplifiedRefreshCallback();

            // 保留原有机制用于兼容性
            await SetupContactComponentReferenceAsync();
            await RegisterUnifiedCacheCallbacks();
        }
    }

    /// <summary>
    /// 初始化SignalR连接 - 增强JavaScript就绪检查
    /// </summary>
    private async Task InitializeSignalRAsync()
    {
        try
        {
            // 检查Circuit状态
            if (!await IsCircuitHealthyAsync())
            {
                Logger.LogDebug("Circuit不健康，跳过SignalR初始化");
                return;
            }

            // 🔧 修复：等待JavaScript函数就绪
            var jsReady = await WaitForJavaScriptFunctionAsync("initializeUnifiedSignalR", TimeSpan.FromSeconds(15));
            if (!jsReady)
            {
                Logger.LogWarning("JavaScript函数initializeUnifiedSignalR未就绪，跳过SignalR初始化");
                return;
            }

            // 使用统一初始化方法
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
            await JSRuntime.InvokeVoidAsync("initializeUnifiedSignalR", cts.Token);

            // 如果有选中的账户，立即加入组
            if (HasSelectedAccount)
            {
                // 🔧 注释冗余的SignalR连接日志 - 降低日志噪音
                // Logger.LogInformation("🔗 尝试加入微信管理器组 - WxManagerId: {WxManagerId}", SelectedAccount!.Id);
                await JSRuntime.InvokeVoidAsync("joinWxManagerGroup", SelectedAccount.Id.ToString());
                // 🔧 注释冗余的SignalR连接日志 - 降低日志噪音
                // Logger.LogInformation("✅ 已加入微信管理器组");
            }
        }
        catch (TaskCanceledException)
        {
            Logger.LogDebug("SignalR初始化超时");
        }
        catch (JSDisconnectedException)
        {
            Logger.LogDebug("Circuit已断开，跳过SignalR初始化");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ SignalR连接初始化失败");
        }
    }

    /// <summary>
    /// 简化的组件引用设置 - 专注于核心功能，减少复杂验证
    /// </summary>
    private async Task SetupContactComponentReferenceAsync()
    {
        try
        {
            // 🔧 注释冗余的组件引用设置日志 - 减少日志噪音
            // Logger.LogInformation("🔄 开始设置联系人组件引用（简化版）");

            // 创建组件引用
            _dotNetRefContact = DotNetObjectReference.Create(this);

            // 尝试设置组件引用，但不强制要求成功
            try
            {
                await JSRuntime.InvokeVoidAsync("setGlobalContactComponent", _dotNetRefContact);
                // 🔧 注释冗余的组件引用日志 - 减少日志噪音
                // Logger.LogInformation("✅ 联系人组件引用设置完成");
            }
            catch (Exception ex)
            {
                // 🔧 注释冗余的警告日志 - 减少日志噪音
                // Logger.LogWarning(ex, "⚠️ 设置联系人组件引用失败，但不影响核心功能");
            }

            // 尝试设置到统一缓存管理器，但不强制要求成功
            try
            {
                await JSRuntime.InvokeVoidAsync("setUnifiedCacheManagerComponent", "contact", _dotNetRefContact);
                Logger.LogDebug("✅ 统一缓存管理器组件引用设置完成");
            }
            catch (Exception ex)
            {
                // 🔧 注释冗余的警告日志 - 减少日志噪音，这是已知的非关键错误
                // Logger.LogWarning(ex, "⚠️ 设置统一缓存管理器组件引用失败，但不影响核心功能");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 组件引用设置过程出错，但不影响数据加载");
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        // 重新启用参数设置处理，确保页面切换时状态正确
        await base.OnParametersSetAsync();

        // 联系人页面特有的参数设置逻辑
        if (HasSelectedAccount)
        {
            Logger.LogDebug("📄 联系人页面参数更新 - SelectedAccount: {AccountId}", SelectedAccount!.Id);

            // 强制UI刷新以确保数据显示
            await InvokeAsync(StateHasChanged);
        }
    }

    // 实现基类抽象方法
    protected override async Task LoadInitialDataAsync()
    {
        try
        {
            // 🔧 注释冗余的页面加载开始日志 - 减少日志噪音
            // Logger.LogInformation("🚀 联系人页面初始数据加载开始");

            // 检查是否需要强制刷新
            if (HasSelectedAccount && await DataSyncManager.ShouldRefreshUI(SelectedAccount!.Id))
            {
                Logger.LogInformation("🔄 检测到数据变更，清除缓存后加载");
                await DataSyncManager.MarkUIRefreshed(SelectedAccount!.Id);
                // 清除本地缓存
                _tabDataCache.Clear();
            }

            // 立即显示基础数据，AI配置异步加载
            await OnSearchCore();
            // 🔧 注释冗余的页面加载日志 - 减少日志噪音
            // Logger.LogInformation("✅ 联系人页面初始数据加载完成");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "联系人初始数据加载失败");
            throw;
        }
    }

    protected override async Task LoadCurrentPageDataAsync()
    {
        try
        {
            // 🔧 注释冗余的页面加载开始日志 - 减少日志噪音
            // Logger.LogInformation("🔄 联系人页面当前数据加载开始 - TabIndex: {TabIndex}", _activeTabIndex);

            // 检查是否需要强制刷新
            if (HasSelectedAccount && await DataSyncManager.ShouldRefreshUI(SelectedAccount!.Id))
            {
                Logger.LogInformation("🔄 检测到数据变更，强制刷新");
                await DataSyncManager.MarkUIRefreshed(SelectedAccount!.Id);
                _tabDataCache.Clear();
                await OnSearchCore(useCache: false); // 强制从服务器加载
            }
            else
            {
                await OnSearchCore(useCache: true);
            }

            // 强制UI更新
            await InvokeAsync(StateHasChanged);
            // 🔧 注释冗余的页面加载完成日志 - 减少日志噪音
            // Logger.LogInformation("✅ 联系人页面当前数据加载完成");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "联系人当前页面数据加载失败");
            throw;
        }
    }

    // 🚀 简化：删除复杂的进度更新处理，只保留同步完成处理

    /// <summary>
    /// 联系人同步完成回调（简化版本）
    /// </summary>
    [JSInvokable]
    public async Task OnSyncCompleted(object completionData)
    {
        try
        {
            Logger.LogInformation("🎉 联系人同步完成回调被调用");

            var json = System.Text.Json.JsonSerializer.Serialize(completionData);
            Logger.LogDebug("🔍 接收到的完成数据: {Data}", json);

            // 解析完成数据
            var jsonDoc = System.Text.Json.JsonDocument.Parse(json);
            var root = jsonDoc.RootElement;

            // 获取微信管理器ID
            var wxManagerIdStr = root.TryGetProperty("WxManagerId", out var wxManagerIdProp) ? wxManagerIdProp.GetString() :
                                root.TryGetProperty("wxManagerId", out var wxManagerIdProp2) ? wxManagerIdProp2.GetString() : null;

            if (Guid.TryParse(wxManagerIdStr, out var wxManagerId) && HasSelectedAccount && wxManagerId == SelectedAccount.Id)
            {
                // 重置同步状态
                _isSyncing = false;

                Snackbar.Add("联系人同步完成！正在刷新数据...", Severity.Success);

                // 使用统一前端缓存服务处理同步完成
                await HandleContactSyncCompleted();

                await InvokeAsync(StateHasChanged);

                Logger.LogInformation("✅ 联系人同步完成处理结束");
            }
            else
            {
                Logger.LogDebug("🔍 同步完成通知不匹配当前账户，忽略处理");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ [前端] 处理数据刷新完成失败");
            _isSyncing = false; // 确保状态重置
            await InvokeAsync(StateHasChanged);
            Snackbar.Add("数据刷新处理异常，请手动刷新页面", Severity.Error);
        }
    }



    /// <summary>
    /// 处理联系人同步完成（统一缓存服务自动处理数据刷新）
    /// </summary>
    private async Task HandleContactSyncCompleted()
    {
        try
        {
            if (HasSelectedAccount)
            {
                Logger.LogInformation("🔄 [前端] 使用统一缓存服务处理同步完成");

                // 🔧 确保重置同步状态
                _isSyncing = false;
                await InvokeAsync(StateHasChanged);

                var result = await UnifiedFrontendCacheService.SmartClearAsync(
                    SelectedAccount!.Id,
                    FrontendCacheOperationType.ContactSyncCompleted);

                if (result.Success)
                {
                    Logger.LogDebug("✅ 同步完成缓存清理成功 - 耗时: {ElapsedMs}ms, 数据刷新已自动触发", result.ElapsedMs);
                }
                else
                {
                    Logger.LogWarning("⚠️ 同步完成缓存清理失败: {ErrorMessage}", result.ErrorMessage);
                    // 降级到手动刷新
                    _tabDataCache.Clear();
                    await OnSearchCore(useCache: false);
                }

                // 🔧 确保最终状态重置
                if (_isSyncing)
                {
                    _isSyncing = false;
                    await InvokeAsync(StateHasChanged);
                    Logger.LogInformation("🔄 最终重置联系人同步按钮状态");
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 处理联系人同步完成失败");

            // 🔧 确保异常情况下也重置状态
            _isSyncing = false;
            await InvokeAsync(StateHasChanged);

            // 降级到原有方案
            _tabDataCache.Clear();
            await OnSearchCore(useCache: false);
        }
    }

    /// <summary>
    /// 数据刷新回调（由统一前端缓存服务自动调用）- 优化版本
    /// </summary>
    private async Task OnDataRefreshCallback(FrontendCacheRefreshContext context)
    {
        try
        {
            // 检查是否是当前选中的账号
            if (HasSelectedAccount && SelectedAccount!.Id == context.WxManagerId)
            {
                Logger.LogInformation("🔄 [自动刷新] 收到数据刷新通知 - ManagerId: {ManagerId}, OperationType: {OperationType}",
                    context.WxManagerId, context.OperationType);

                // 🔥 优化：检查操作类型，避免与强制刷新冲突
                var shouldSkipRefresh = context.OperationType switch
                {
                    FrontendCacheOperationType.BatchDelete => true, // 批量删除由强制刷新处理
                    FrontendCacheOperationType.SingleDelete => true, // 单个删除也由强制刷新处理
                    _ => false
                };

                if (shouldSkipRefresh)
                {
                    Logger.LogDebug("⏭️ 跳过自动刷新回调 - OperationType: {OperationType}，由强制刷新处理", context.OperationType);
                    return;
                }

                // 清空缓存并强制重新加载数据
                _tabDataCache.Clear();
                await OnSearchCore(useCache: false);
                await InvokeAsync(StateHasChanged);

                Logger.LogInformation("✅ [自动刷新] 数据刷新完成");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ [自动刷新] 数据刷新回调执行失败");
        }
    }

    /// <summary>
    /// 注册统一缓存回调
    /// </summary>
    private async Task RegisterUnifiedCacheCallbacks()
    {
        try
        {
            // 注册统一前端缓存服务回调（使用现有的组件引用机制）
            UnifiedFrontendCacheService.RegisterCacheRefreshCallback(async (context) =>
            {
                if (HasSelectedAccount && context.WxManagerId == SelectedAccount!.Id)
                {
                    Logger.LogDebug("🔄 [前端] 统一缓存服务回调触发 - OperationType: {OperationType}", context.OperationType);
                    await InvokeAsync(async () =>
                    {
                        await OnSearchCore(useCache: false);
                        StateHasChanged();
                    });
                }
            });

            Logger.LogDebug("✅ [前端] 统一缓存回调注册完成");
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "⚠️ [前端] 统一缓存回调注册失败");
        }
    }

    /// <summary>
    /// JavaScript调用的统一缓存清理回调方法（合并版本）
    /// </summary>
    [JSInvokable]
    public async Task OnUnifiedCacheCleared(string wxManagerId, string dataType)
    {
        try
        {
            Console.WriteLine($"🧹 [前端] 统一缓存清理完成回调 - ManagerId: {wxManagerId}, DataType: {dataType}");

            // 参数验证和类型转换
            if (string.IsNullOrEmpty(wxManagerId))
            {
                Logger.LogWarning("⚠️ [JavaScript] wxManagerId参数为空，跳过处理");
                return;
            }

            if (!Guid.TryParse(wxManagerId, out var managerGuid))
            {
                Logger.LogWarning("⚠️ [JavaScript] wxManagerId格式无效: {WxManagerId}", wxManagerId);
                return;
            }

            if (HasSelectedAccount && SelectedAccount!.Id == managerGuid)
            {
                Logger.LogInformation("🔄 [JavaScript] 统一缓存清理回调触发 - WxManagerId: {WxManagerId}, DataType: {DataType}",
                    managerGuid, dataType);

                // 强制重新加载数据
                await InvokeAsync(async () =>
                {
                    await OnSearchCore(useCache: false);
                    StateHasChanged();
                });

                Logger.LogInformation("✅ [JavaScript] 统一缓存清理后数据刷新完成");
            }
            else
            {
                Logger.LogDebug("🔍 [JavaScript] 管理器ID不匹配，跳过处理 - 当前: {Current}, 请求: {Requested}",
                    HasSelectedAccount ? SelectedAccount!.Id : Guid.Empty, managerGuid);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ [JavaScript] 统一缓存清理回调处理失败");
        }
    }



    /// <summary>
    /// 手动验证和修复缓存一致性（供调试和紧急情况使用）
    /// </summary>
    private async Task ManualValidateAndFixCache()
    {
        try
        {
            if (!HasSelectedAccount) return;

            Logger.LogInformation("🔧 开始手动验证和修复缓存一致性");
            Snackbar.Add("正在验证和修复缓存一致性...", Severity.Info);

            var isConsistent = await UnifiedFrontendCacheService.ValidateAndRecoverCacheConsistencyAsync(
                SelectedAccount!.Id,
                FrontendCacheOperationType.ManualRefresh);

            if (isConsistent)
            {
                Snackbar.Add("缓存一致性验证通过", Severity.Success);
                Logger.LogInformation("✅ 手动缓存一致性验证通过");
            }
            else
            {
                Snackbar.Add("缓存一致性修复完成，请检查数据", Severity.Warning);
                Logger.LogWarning("⚠️ 手动缓存一致性修复完成，但可能仍存在问题");
            }

            // 强制重新加载数据
            await OnSearchCore(useCache: false);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 手动验证和修复缓存一致性失败");
            Snackbar.Add("缓存修复失败，请联系技术支持", Severity.Error);
        }
    }

    /// <summary>
    /// 验证数据库中的联系人数量
    /// </summary>
    private async Task<int> VerifyDatabaseContactCount()
    {
        try
        {
            if (!HasSelectedAccount) return 0;

            // 直接查询数据库中的联系人数量
            var query = new GetContactOrGroupListQuery
            {
                WxManagerId = SelectedAccount!.Id,
                ContactTypes = new List<WxContactType> { WxContactType.Contact, WxContactType.Enterprise },
                SearchedNickName = "",
                SearchedRemarkName = "",
                PageQuery = new PageQuery { Page = 1, PageSize = 1000 } // 获取大量数据以确保不遗漏
            };

            var result = await WxApi.getContactOrGroupList(query);
            var dbCount = result?.TotalCount ?? 0;

            Logger.LogInformation("📊 数据库联系人验证 - 总数: {Count}", dbCount);
            return (int)dbCount;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 验证数据库联系人数量失败");
            return 0;
        }
    }





    [JSInvokable]
    public async Task OnSyncFailed(object errorData)
    {
        try
        {
            var json = System.Text.Json.JsonSerializer.Serialize(errorData);
            Logger.LogError("❌ 联系人同步失败: {Error}", json);

            // 🚀 简化：重置同步状态
            _isSyncing = false;

            Snackbar.Add("联系人同步失败，请稍后重试", Severity.Error);
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "处理同步失败回调异常");

            // 确保状态重置
            _isSyncing = false;
            await InvokeAsync(StateHasChanged);
        }
    }





    [JSInvokable]
    public async Task OnConfigurationChanged(object configData)
    {
        try
        {
            var json = System.Text.Json.JsonSerializer.Serialize(configData);
            var data = System.Text.Json.JsonSerializer.Deserialize<dynamic>(json);

            Logger.LogInformation("收到联系人配置变更通知: {ConfigData}", json);

            // 刷新联系人列表和AI配置
            await OnSearch();

            // 重新加载联系人AI配置
            await LoadContactsAiConfigAsync();

            Snackbar.Add("联系人配置已更新", Severity.Info);
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "处理联系人配置变更通知失败");
        }
    }

    public override void Dispose()
    {
        try
        {
            // 注销数据刷新回调
            if (!string.IsNullOrEmpty(_refreshCallbackId))
            {
                UnifiedFrontendCacheService.UnregisterDataRefreshCallback(_refreshCallbackId);
            }

            // 联系人页面特有的清理逻辑
            SearchDebounceService?.CancelSearch(SEARCH_KEY);

            // 🚀 简化：删除进度监控定时器清理

            // 清理联系人页面专用的DotNetObjectReference
            _dotNetRefContact?.Dispose();

            // 🔧 注释冗余的页面资源清理日志 - 降低日志噪音
            // Logger.LogInformation("联系人页面资源清理完成");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "联系人页面资源清理异常");
        }
        finally
        {
            // 调用基类的清理方法
            base.Dispose();
        }
    }

    // 重复方法已移至基类

    private async Task GetContactsForAccount(Guid wxManagerId)
    {
        // 🚀 简化：防重复点击检查
        if (_isSyncing)
        {
            Snackbar.Add("联系人同步任务正在进行中，请稍候...", Severity.Warning);
            return;
        }

        // 数据预检查
        if (!await ValidateContactSyncPrerequisites(wxManagerId))
        {
            return;
        }

        // 执行同步操作
        try
        {
            // 设置按钮加载状态
            _isSyncing = true;
            await InvokeAsync(StateHasChanged);

            // 🔧 使用DataSyncOrchestrator触发联系人详情获取
            var command = new GetContactDetailsCommand
            {
                WxManagerId = wxManagerId,
                ListTypes = new List<WxContactListType> { WxContactListType.Friends, WxContactListType.Enterprise }
            };

            var result = await DataSyncOrchestrator.TriggerContactDetailsAsync(command);

            if (result)
            {
                Snackbar.Add("联系人同步任务已加入队列，正在处理中...", Severity.Info);

                // 加入SignalR组以接收同步完成通知
                await JoinWxManagerGroupAsync(wxManagerId);

                // 🔧 启动智能超时和恢复机制
                _ = Task.Run(async () =>
                {
                    var initialDelay = 5000;  // 初始等待5秒
                    var shortTimeout = 30000; // 30秒短超时
                    var longTimeout = 120000; // 2分钟长超时

                    await Task.Delay(initialDelay);
                    
                    // 第一次检查：5秒后检查
                    if (_isSyncing)
                    {
                        Logger.LogDebug("🔍 第一次同步状态检查 - WxManagerId: {WxManagerId}", wxManagerId);
                        
                        // 等待30秒
                        await Task.Delay(shortTimeout - initialDelay);
                        
                        // 第二次检查：30秒后仍在同步
                        if (_isSyncing)
                        {
                            Logger.LogWarning("⚠️ 同步可能出现问题，等待更长时间 - WxManagerId: {WxManagerId}", wxManagerId);
                            await InvokeAsync(() => Snackbar.Add("同步正在进行中，请耐心等待...", Severity.Info));

                            // 🔧 在30秒后开始主动检查同步状态
                            var checkCount = 0;
                            var maxChecks = 18; // 90秒内检查18次，每5秒一次

                            while (_isSyncing && checkCount < maxChecks)
                            {
                                await Task.Delay(5000); // 每5秒检查一次
                                checkCount++;

                                try
                                {
                                    Logger.LogDebug("🔍 主动检查联系人同步状态 - 第{Count}次检查", checkCount);

                                    // 尝试强制刷新数据，看是否有新的联系人数据
                                    await InvokeAsync(async () =>
                                    {
                                        try
                                        {
                                            await OnSearchCore(useCache: false);
                                            Logger.LogInformation("✅ 主动数据刷新完成，检查是否有新数据");
                                        }
                                        catch (Exception ex)
                                        {
                                            Logger.LogWarning(ex, "⚠️ 主动数据刷新失败");
                                        }
                                    });
                                }
                                catch (Exception ex)
                                {
                                    Logger.LogWarning(ex, "⚠️ 联系人同步状态检查失败");
                                }
                            }

                            // 最终检查：2分钟后强制重置
                            if (_isSyncing)
                            {
                                Logger.LogError("❌ 同步超时强制重置 - WxManagerId: {WxManagerId}", wxManagerId);
                                _isSyncing = false;
                                await InvokeAsync(StateHasChanged);
                                await InvokeAsync(() => Snackbar.Add("同步超时已重置，请检查网络连接或稍后重试", Severity.Error));
                                
                                // 尝试强制刷新数据
                                _ = Task.Run(async () =>
                                {
                                    await Task.Delay(2000);
                                    await InvokeAsync(async () => await SimplifiedRefresh());
                                });
                            }
                        }
                    }
                });
            }
            else
            {
                _isSyncing = false;
                await InvokeAsync(StateHasChanged);
                Snackbar.Add("获取联系人失败：服务器返回空结果", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "获取联系人失败 - WxManagerId: {WxManagerId}", wxManagerId);
            _isSyncing = false;
            await InvokeAsync(StateHasChanged);

            var errorMessage = GetUserFriendlyErrorMessage(ex);
            Snackbar.Add($"获取联系人失败: {errorMessage}", Severity.Error);
        }
    }

    // 联系人页面专用的DotNetObjectReference
    private DotNetObjectReference<WxContact>? _dotNetRefContact;



    /// <summary>
    /// 注册联系人特定的SignalR回调（不设置组件引用，避免时序问题）
    /// </summary>
    protected async Task RegisterPageSpecificCallbacks()
    {
        try
        {
            // 只注册联系人特定回调，组件引用在OnAfterRenderAsync中设置
            await JSRuntime.InvokeVoidAsync("registerContactCallbacks");

            Logger.LogDebug("✅ 联系人页面SignalR回调注册成功");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 联系人页面SignalR回调注册失败");
        }
    }

    /// <summary>
    /// 验证联系人ID格式
    /// </summary>
    private string? ValidateContactId(string contactId)
    {
        if (string.IsNullOrWhiteSpace(contactId))
            return null;

        contactId = contactId.Trim();

        // 检查长度
        if (contactId.Length < 3)
            return "联系人ID长度不能少于3位";

        if (contactId.Length > 50)
            return "联系人ID长度不能超过50位";

        // 检查是否包含非法字符
        if (contactId.Contains(" "))
            return "联系人ID不能包含空格";

        // 简单的格式验证
        // wcid通常是数字，微信ID通常是字母数字组合，手机号是纯数字
        if (!System.Text.RegularExpressions.Regex.IsMatch(contactId, @"^[a-zA-Z0-9_\-@\.]+$"))
            return "联系人ID格式不正确，只能包含字母、数字、下划线、连字符、@符号和点号";

        return null;
    }

    /// <summary>
    /// 处理输入框键盘事件
    /// </summary>
    private async Task OnContactIdKeyPress(KeyboardEventArgs e)
    {
        // 回车键快速添加
        if (e.Key == "Enter" && !string.IsNullOrEmpty(manualContactId?.Trim()) && HasSelectedAccount && !_isAddingContact)
        {
            await AddContactManually();
        }
    }

    private async Task AddContactManually()
    {
        if (!HasSelectedAccount)
        {
            Snackbar.Add("请先选择微信账号", Severity.Warning);
            return;
        }

        if (_isSyncing)
        {
            Snackbar.Add("系统正在处理其他操作，请稍候...", Severity.Warning);
            return;
        }

        var trimmedContactId = manualContactId?.Trim();
        if (string.IsNullOrEmpty(trimmedContactId))
        {
            Snackbar.Add("请输入联系人ID", Severity.Warning);
            return;
        }

        // 验证输入格式
        var validationError = ValidateContactId(trimmedContactId);
        if (!string.IsNullOrEmpty(validationError))
        {
            Snackbar.Add(validationError, Severity.Error);
            return;
        }

        // 检查是否已存在
        var existingContact = Elements.FirstOrDefault(c =>
            c.WcId.Equals(trimmedContactId, StringComparison.OrdinalIgnoreCase) ||
            c.NickName.Equals(trimmedContactId, StringComparison.OrdinalIgnoreCase));

        if (existingContact != null)
        {
            Snackbar.Add($"联系人 {existingContact.NickName} 已存在", Severity.Warning);
            return;
        }

        _isAddingContact = true;
        await InvokeAsync(StateHasChanged);

        try
        {
            Logger.LogInformation("开始手动添加联系人 - ContactId: {ContactId}, WxManagerId: {WxManagerId}",
                trimmedContactId, SelectedAccount!.Id);

            var command = new AddContactManuallyCommand
            {
                WxManagerId = SelectedAccount!.Id,
                ContactId = trimmedContactId,
                ListType = manualContactType
            };

            var result = await WxApi.AddContactManually(command);
            if (result)
            {
                Snackbar.Add($"联系人 {trimmedContactId} 添加成功", Severity.Success);
                manualContactId = "";

                // 清除缓存并刷新数据
                _tabDataCache.Remove(SelectedAccount!.Id);
                await UnifiedDataManager.InvalidateCacheAsync(SelectedAccount!.Id);

                // 延迟一点时间再刷新，让后端有时间处理
                await Task.Delay(1500);
                await OnSearchCore(useCache: false); // 强制从数据库重新加载

                Logger.LogInformation("手动添加联系人成功 - ContactId: {ContactId}", trimmedContactId);
            }
            else
            {
                Snackbar.Add($"联系人 {trimmedContactId} 添加失败，请检查ID是否正确", Severity.Error);
                Logger.LogWarning("手动添加联系人失败 - ContactId: {ContactId}, 返回结果为false", trimmedContactId);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "手动添加联系人异常 - ContactId: {ContactId}", trimmedContactId);

            var errorMessage = GetUserFriendlyErrorMessage(ex);
            Snackbar.Add($"添加联系人失败: {errorMessage}", Severity.Error);
        }
        finally
        {
            _isAddingContact = false;
            await InvokeAsync(StateHasChanged);
        }
    }

    private async Task GetContactsForCurrentAccount()
    {
        if (!HasSelectedAccount)
        {
            Snackbar.Add("请先选择微信账号", Severity.Warning);
            return;
        }

        await GetContactsForAccount(SelectedAccount!.Id);
    }

    // 🚀 简化：删除复杂的操作状态重置逻辑

    // 🚀 简化：删除强制重置同步状态方法

    // 🚀 简化：删除状态检查定时器

    /// <summary>
    /// 从API检查同步状态
    /// </summary>
    private async Task CheckSyncStatusFromApi()
    {
        try
        {
            if (!_isSyncing) return;

            Logger.LogDebug("🔍 执行API状态检查 - WxManagerId: {WxManagerId}", SelectedAccount!.Id);

            var response = await HttpClient.GetAsync($"/api/SyncStatus/contact/{SelectedAccount!.Id}");
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<ApiResponse<ContactSyncStatusDto>>();
                if (result?.Success == true && result.Data != null)
                {
                    var status = result.Data;

                    Logger.LogInformation("📊 API状态检查结果 - Status: {Status}, Progress: {Progress}%, Processed: {Processed}/{Total}",
                        status.Status, status.ProgressPercentage, status.ProcessedCount, status.TotalCount);

                    // 检查是否卡死
                    if (status.IsStuck)
                    {
                        Logger.LogWarning("检测到卡死的同步状态，自动重置");
                        _isSyncing = false;
                        await InvokeAsync(StateHasChanged);
                        await InvokeAsync(() => Snackbar.Add("检测到卡死的同步状态，已自动重置", Severity.Warning));
                        return;
                    }

                    // 检查同步是否已完成
                    if (status.Status == SyncStatus.Completed)
                    {
                        Logger.LogInformation("✅ API检查发现同步已完成 - WxManagerId: {WxManagerId}, Success: {Success}, Failed: {Failed}",
                            SelectedAccount!.Id, status.SuccessCount, status.FailedCount);

                        // 重置同步状态
                        _isSyncing = false;

                        // 显示完成消息
                        await InvokeAsync(() => Snackbar.Add($"联系人同步完成！成功: {status.SuccessCount}, 失败: {status.FailedCount}", Severity.Success));

                        // 刷新数据
                        await InvokeAsync(async () =>
                        {
                            await HandleContactSyncCompleted();
                            StateHasChanged();
                        });

                        return;
                    }

                    // 检查同步是否失败
                    if (status.Status == SyncStatus.Failed)
                    {
                        Logger.LogWarning("❌ API检查发现同步失败 - WxManagerId: {WxManagerId}, Error: {Error}",
                            SelectedAccount!.Id, status.ErrorMessage);

                        _isSyncing = false;
                        await InvokeAsync(StateHasChanged);
                        await InvokeAsync(() => Snackbar.Add($"联系人同步失败: {status.ErrorMessage}", Severity.Error));
                        return;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "从API检查同步状态异常");
        }
    }

    // 🚀 简化：删除重复的同步完成处理逻辑

    // 🚀 简化：删除复杂的操作状态管理方法

    /// <summary>
    /// 验证联系人同步前置条件
    /// </summary>
    private async Task<bool> ValidateContactSyncPrerequisites(Guid wxManagerId)
    {
        try
        {
            // 检查微信账号状态
            var account = wxAccounts.FirstOrDefault(a => a.Id == wxManagerId);
            if (account == null)
            {
                Snackbar.Add("找不到指定的微信账号", Severity.Error);
                return false;
            }

            if (account.WxStatus != WxStatus.AlreadyLogIn)
            {
                Snackbar.Add($"微信账号 {account.NickName} 未登录，请先登录", Severity.Warning);
                return false;
            }

            if (!account.IsEnabled)
            {
                Snackbar.Add($"微信账号 {account.NickName} 已被禁用", Severity.Warning);
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "验证联系人同步前置条件失败");
            Snackbar.Add("系统检查失败，请稍后重试", Severity.Error);
            return false;
        }
    }

    /// <summary>
    /// 获取用户友好的错误消息
    /// </summary>
    private string GetUserFriendlyErrorMessage(Exception ex)
    {
        var message = ex.Message;

        if (message.Contains("微信未登录") || message.Contains("会话已过期"))
        {
            return "微信登录状态异常，请重新登录微信";
        }
        else if (message.Contains("网络") || message.Contains("连接"))
        {
            return "网络连接异常，请检查网络后重试";
        }
        else if (message.Contains("权限") || message.Contains("授权"))
        {
            return "权限不足，请联系管理员";
        }
        else if (message.Contains("超时"))
        {
            return "操作超时，请稍后重试";
        }
        else
        {
            return message.Length > 100 ? message.Substring(0, 100) + "..." : message;
        }
    }

    // 🚀 简化：删除复杂的进度监控逻辑

    private async Task ShowBatchEditDialog()
    {
        if (_selectedContactIds.Count == 0)
        {
            Snackbar.Add("请先选择要编辑的联系人", Severity.Warning);
            return;
        }

        var selectedContacts = GetSelectedContacts();

        var parameters = new DialogParameters
        {
            ["SelectedContacts"] = selectedContacts,
            ["AiAgents"] = aiAgents,
            ["OnBatchOperationCompleted"] = EventCallback.Factory.Create(this, OnBatchOperationCompleted)
        };

        var options = new DialogOptions
        {
            MaxWidth = MaxWidth.Large,
            FullWidth = true,
            CloseButton = true,
            CloseOnEscapeKey = false
        };

        await DialogService.ShowAsync<Components.Wx.BatchEditDialog>("批量编辑联系人", parameters, options);
    }

    private async Task OnBatchOperationCompleted()
    {
        try
        {
            Logger.LogDebug("🔄 批量操作完成回调开始");

            // 清空选择
            _selectedContactIds.Clear();
            UpdateSelectAllState();

            // 🔥 优化：检查是否需要额外刷新
            // 如果强制刷新已经处理了数据更新，则跳过额外的刷新
            var needsAdditionalRefresh = await ShouldPerformAdditionalRefresh();

            if (needsAdditionalRefresh)
            {
                Logger.LogDebug("🔄 执行额外的数据刷新");
                await SimplifiedRefresh();
            }
            else
            {
                Logger.LogDebug("⏭️ 跳过额外刷新，直接重新加载数据");
                // 直接重新加载数据，不调用SimplifiedRefresh
                await OnSearchCore(useCache: false);
            }

            // 确保所有状态都被正确重置
            _isLoading = false;

            Logger.LogDebug("✅ 批量操作完成回调结束");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 批量操作完成回调失败");

            // 即使出错也要重置状态
            _isLoading = false;
        }
        finally
        {
            // 确保UI状态更新
            StateHasChanged();
        }
    }

    /// <summary>
    /// 检查是否需要执行额外的刷新操作
    /// </summary>
    private async Task<bool> ShouldPerformAdditionalRefresh()
    {
        try
        {
            if (!HasSelectedAccount)
            {
                return false;
            }

            // 检查最近是否有强制刷新操作
            // 如果5秒内有强制刷新，则跳过额外刷新
            var recentRefreshWindow = TimeSpan.FromSeconds(5);

            // 这里可以添加更复杂的逻辑来检查刷新状态
            // 目前简化为延迟检查，避免与强制刷新冲突
            await Task.Delay(100);

            return false; // 默认跳过额外刷新，由强制刷新处理
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "⚠️ 检查额外刷新需求时发生异常");
            return true; // 异常时执行刷新作为安全措施
        }
    }

    private async Task RefreshWithRetry(int maxRetries = 2)
    {
        var retryCount = 0;
        var refreshSuccessful = false;

        while (retryCount <= maxRetries && !refreshSuccessful)
        {
            try
            {
                if (retryCount > 0)
                {
                    // 重试时显示状态提示
                    Snackbar.Add($"正在刷新数据... ({retryCount}/{maxRetries})", Severity.Info);

                    // 重试前等待更长时间
                    await Task.Delay(1000 * retryCount);
                }

                // 记录刷新前的数据状态用于验证
                var elementsCountBefore = Elements?.Count() ?? 0;

                // 强制清除所有缓存并重新加载数据
                await SimplifiedRefresh();

                // 验证刷新是否成功
                var validationResult = await ValidateDataConsistency(elementsCountBefore);

                if (validationResult.IsValid)
                {
                    refreshSuccessful = true;

                    if (retryCount > 0)
                    {
                        Snackbar.Add("数据刷新成功", Severity.Success);
                    }
                }
                else
                {
                    if (retryCount == maxRetries)
                    {
                        Snackbar.Add($"数据状态可能不一致: {validationResult.Message}", Severity.Warning);
                    }
                }

            }
            catch (Exception)
            {
                if (retryCount == maxRetries)
                {
                    Snackbar.Add("数据刷新失败，请手动刷新页面", Severity.Warning);
                }
            }

            retryCount++;
        }
    }

    /// <summary>
    /// 注册简化刷新回调
    /// </summary>
    private async Task RegisterSimplifiedRefreshCallback()
    {
        try
        {
            // 检查Circuit状态
            if (!await IsCircuitHealthyAsync())
            {
                Logger.LogDebug("Circuit不健康，跳过刷新回调注册");
                return;
            }
            // 创建刷新回调函数
            var refreshCallback = DotNetObjectReference.Create(this);

            // 注册到简化刷新管理器
            await JSRuntime.InvokeVoidAsync("registerPageRefresh", "contact", refreshCallback);

            Logger.LogDebug("✅ 简化刷新回调注册成功");
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "⚠️ 简化刷新回调注册失败");
        }
    }

    /// <summary>
    /// JavaScript调用的简化刷新方法
    /// </summary>
    [JSInvokable]
    public async Task OnSimplifiedRefresh(object refreshData)
    {
        try
        {
            Logger.LogDebug("🔄 收到简化刷新请求: {RefreshData}", refreshData);

            await InvokeAsync(async () =>
            {
                await SimplifiedRefresh();

                // 确保所有加载状态都被重置
                _isLoading = false;
                _isSyncing = false; // 重置同步状态

                // 立即更新UI状态
                StateHasChanged();
            });
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 简化刷新处理失败");

            // 即使出错也要重置加载状态
            await InvokeAsync(() =>
            {
                _isLoading = false;
                _isSyncing = false; // 重置同步状态
                StateHasChanged();
            });
        }
    }

    /// <summary>
    /// 简化的数据刷新方法
    /// </summary>
    private async Task SimplifiedRefresh()
    {
        try
        {
            if (!HasSelectedAccount)
            {
                return;
            }

            // 🔥 优化：使用组件级刷新方法，检查批量操作冲突
            var result = await SimplifiedDataRefreshService.ComponentRefreshContactsAsync(SelectedAccount!.Id);

            if (result)
            {
                // 直接重新加载数据
                await OnSearchCore(useCache: false);
                StateHasChanged();
            }
            else
            {
                Logger.LogWarning("简化刷新失败或被跳过");
                // 不显示错误消息，因为可能是正常的跳过
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "简化刷新过程中发生异常");
            Snackbar.Add("数据刷新异常，请重试", Severity.Error);
        }
    }

    private async Task<ValidationResult> ValidateDataConsistency(int expectedCount)
    {
        try
        {
            // 基本数据完整性验证
            if (Elements == null)
            {
                return new ValidationResult { IsValid = false, Message = "数据为空" };
            }

            var currentCount = Elements.Count();

            // 验证数据数量是否合理（允许一定范围的变化）
            if (Math.Abs(currentCount - expectedCount) > expectedCount * 0.5 && expectedCount > 0)
            {
                return new ValidationResult { IsValid = false, Message = $"数据数量异常: 期望约{expectedCount}条，实际{currentCount}条" };
            }

            // 验证AI配置映射是否加载完成
            if (currentCount > 0 && contactAiAgentMapping.Count == 0)
            {
                // AI配置可能还在加载中，给个短暂等待
                await Task.Delay(500);
            }

            return new ValidationResult { IsValid = true, Message = "验证通过" };
        }
        catch (Exception ex)
        {
            return new ValidationResult { IsValid = false, Message = $"验证过程出错: {ex.Message}" };
        }
    }

    private class ValidationResult
    {
        public bool IsValid { get; set; }
        public string Message { get; set; } = "";
    }

    // 加载AI机器人列表
    protected override async Task LoadAiAgentsAsync()
    {
        try
        {
            var query = new AiAgentQuery
            {
                PageQuery = new Application.Commons.PageQuery { Page = 1, PageSize = 1000 },
                IsEnabled = true // 只获取启用的AI机器人
            };

            var response = await AiAgentApi.GetPagedListAsync(query);
            if (response != null)
            {
                aiAgents = response.Items;
            }
        }
        catch (Exception ex)
        {
            // 加载AI机器人列表失败，静默处理
        }
    }

    // 智能初始化数据加载
    private async Task LoadInitialData()
    {
        try
        {
            // 立即显示基础数据，AI配置异步加载
            await OnSearchCore();
        }
        catch (Exception ex)
        {
            // 初始化失败时的降级处理
            await DialogService.ShowMessageBox("页面初始化失败", $"错误信息: {ex.Message}");
        }
    }

    // 异步加载联系人的AI配置（优化同步机制）
    private async Task LoadContactsAiConfigAsync()
    {
        if (Elements == null || !Elements.Any())
            return;

        // 使用锁防止并发调用
        if (_isLoadingAiConfig)
            return;

        try
        {
            _isLoadingAiConfig = true;
            await InvokeAsync(StateHasChanged);

            var contactIds = Elements.Select(c => c.Id).ToList();
            Logger.LogDebug("🔄 开始加载AI配置 - 联系人数量: {Count}", contactIds.Count);

            var aiConfigs = await WxApi.GetContactsAiConfig(contactIds);

            // 同步更新UI状态，确保原子操作
            await InvokeAsync(() =>
            {
                UpdateAiConfigMapping(aiConfigs);
                Logger.LogDebug("✅ AI配置加载完成 - 配置数量: {Count}", aiConfigs?.Count ?? 0);
            });
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "⚠️ AI配置加载失败，但不影响主功能");
        }
        finally
        {
            _isLoadingAiConfig = false;
            await InvokeAsync(StateHasChanged);
        }
    }

    // 带重试机制的AI配置加载方法
    private async Task LoadContactsAiConfigWithRetryAsync()
    {
        if (Elements == null || !Elements.Any())
            return;

        const int maxRetries = 2;
        const int retryDelayMs = 200;

        for (int attempt = 0; attempt <= maxRetries; attempt++)
        {
            try
            {
                var contactIds = Elements.Select(c => c.Id).ToList();
                Logger.LogDebug("🔄 开始加载AI配置（尝试 {Attempt}/{MaxRetries}） - 联系人数量: {Count}",
                    attempt + 1, maxRetries + 1, contactIds.Count);

                var aiConfigs = await WxApi.GetContactsAiConfig(contactIds);

                // 同步更新UI状态
                await InvokeAsync(() =>
                {
                    UpdateAiConfigMapping(aiConfigs);
                    Logger.LogDebug("✅ AI配置加载完成（尝试 {Attempt}） - 配置数量: {Count}",
                        attempt + 1, aiConfigs?.Count ?? 0);
                });

                // 成功加载，退出重试循环
                break;
            }
            catch (Exception ex)
            {
                Logger.LogWarning(ex, "⚠️ AI配置加载失败（尝试 {Attempt}/{MaxRetries}）",
                    attempt + 1, maxRetries + 1);

                // 如果不是最后一次尝试，等待后重试
                if (attempt < maxRetries)
                {
                    await Task.Delay(retryDelayMs);
                }
            }
        }
    }

    // 更新AI配置映射的辅助方法
    private void UpdateAiConfigMapping(List<ContactAiConfigDto> aiConfigs)
    {
        // 清空现有映射
        contactAiAgentMapping.Clear();

        // 更新AI配置映射
        foreach (var config in aiConfigs)
        {
            // 直接通过ContactId匹配
            var contact = Elements.FirstOrDefault(c => c.Id == config.ContactId);
            if (contact != null)
            {
                // 更新AI代理映射
                if (!string.IsNullOrEmpty(config.AiAgentName))
                {
                    contactAiAgentMapping[contact.Id] = config.AiAgentName;
                }

                // 同步更新AutoReplyEnabled状态，确保UI显示与数据库一致
                contact.AutoReplyEnabled = config.IsEnabled;
            }
        }
    }

    // 获取联系人选中的AI机器人名称
    private string GetSelectedAiAgentName(WxContactVo contact)
    {
        return contactAiAgentMapping.TryGetValue(contact.Id, out var agentName) ? agentName : "";
    }

    // AI机器人选择变更事件（事务性同步机制）
    private async Task OnAiAgentChanged(WxContactVo contact, string agentName)
    {
        string? trackingId = null;

        try
        {
            Logger.LogDebug("🔄 开始独立AI配置更新 - Contact: {ContactName}, Agent: {AgentName}", contact.NickName, agentName);

            var operation = string.IsNullOrEmpty(agentName) ? "移除" : "设置";

            // 开始配置生效跟踪
            var effectivenessTracker = ConfigurationEffectivenessTracker;
            if (effectivenessTracker != null)
            {
                trackingId = effectivenessTracker.StartTracking(
                    "ContactAiConfig",
                    $"contact_{contact.Id}",
                    agentName,
                    new List<string> { SelectedAccount?.WcId ?? "" });

                await effectivenessTracker.UpdateStatusAsync(trackingId, ConfigurationEffectivenessStatus.Started, "开始联系人AI配置更新");
            }

            // 使用增强的事务性更新确保数据一致性
            var success = await UpdateAiConfigWithConsistencyAsync(contact, agentName, operation, trackingId);

            if (success)
            {
                Snackbar.Add($"AI机器人配置{operation}成功", Severity.Success);
                Logger.LogDebug("✅ 独立AI配置更新成功 - 其他配置保持不变");

                // 完成跟踪
                if (effectivenessTracker != null && trackingId != null)
                {
                    await effectivenessTracker.CompleteTrackingAsync(trackingId, true);
                }
            }
            else
            {
                throw new Exception("事务性更新失败");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ AI配置事务性更新失败 - Contact: {ContactName}", contact.NickName);
            Snackbar.Add($"AI机器人配置失败: {ex.Message}", Severity.Error);

            // 完成跟踪（失败）
            var effectivenessTracker = ConfigurationEffectivenessTracker;
            if (effectivenessTracker != null && trackingId != null)
            {
                await effectivenessTracker.CompleteTrackingAsync(trackingId, false, ex.Message);
            }

            // 恢复正确状态：重新加载AI配置确保数据一致性
            await LoadContactsAiConfigAsync();
        }
    }

    /// <summary>
    /// 增强的配置更新方法：集成一致性保证和生效跟踪
    /// </summary>
    private async Task<bool> UpdateAiConfigWithConsistencyAsync(WxContactVo contact, string agentName, string operation, string? trackingId)
    {
        try
        {
            // 获取一致性服务
            var consistencyService = ConfigurationConsistencyService;
            var effectivenessTracker = ConfigurationEffectivenessTracker;

            // 更新跟踪状态
            if (effectivenessTracker != null && trackingId != null)
            {
                await effectivenessTracker.UpdateStatusAsync(trackingId, ConfigurationEffectivenessStatus.DatabaseUpdated, "开始数据库更新");
            }

            // 使用原有的事务性更新方法
            var success = await UpdateAiConfigTransactionalAsync(contact, agentName, operation);

            if (success && consistencyService != null)
            {
                // 更新跟踪状态
                if (effectivenessTracker != null && trackingId != null)
                {
                    await effectivenessTracker.UpdateStatusAsync(trackingId, ConfigurationEffectivenessStatus.ActorNotified, "开始配置一致性保证");
                }

                // 确保配置一致性
                var configKey = $"contact_{contact.Id}";
                var affectedWcIds = new List<string> { SelectedAccount?.WcId ?? "" };

                var consistencyResult = await consistencyService.EnsureConfigurationConsistencyAsync(
                    "ContactAiConfig",
                    configKey,
                    agentName,
                    affectedWcIds);

                if (consistencyResult)
                {
                    // 更新跟踪状态
                    if (effectivenessTracker != null && trackingId != null)
                    {
                        await effectivenessTracker.UpdateStatusAsync(trackingId, ConfigurationEffectivenessStatus.Effective, "配置一致性保证成功");
                    }

                    // 🔧 注释冗余的一致性保证成功日志 - 减少日志噪音
                    // Logger.LogInformation("✅ 联系人AI配置一致性保证成功 - Contact: {ContactName}, Agent: {AgentName}",
                    //     contact.NickName, agentName);
                }
                else
                {
                    Logger.LogWarning("⚠️ 联系人AI配置一致性保证失败，但配置已更新 - Contact: {ContactName}", contact.NickName);
                }
            }

            return success;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 增强配置更新失败 - Contact: {ContactName}", contact.NickName);
            return false;
        }
    }

    /// <summary>
    /// 事务性AI配置更新：确保数据一致性
    /// </summary>
    private async Task<bool> UpdateAiConfigTransactionalAsync(WxContactVo contact, string agentName, string operation)
    {
        // 使用标准化数据管理器的事务性操作
        return await UpdateAiConfigWithStandardizedManagerAsync(contact, agentName, operation);
        var maxRetries = 3;
        var retryCount = 0;

        while (retryCount < maxRetries)
        {
            try
            {
                // 开始事务性更新
                Logger.LogDebug("🔄 事务性AI配置更新 - 尝试 {Retry}/{Max}", retryCount + 1, maxRetries);

                // 1. 预检查：验证联系人状态
                if (contact.WxManagerId == Guid.Empty)
                {
                    throw new InvalidOperationException("联系人微信管理器ID无效");
                }

                // 2. 执行API调用 - 仅设置AI代理，不影响自动回复状态
                var command = new Application.DTOs.Requests.Commands.SetContactAiAgentCommand
                {
                    ContactId = contact.Id,
                    WxManagerId = contact.WxManagerId,
                    AiAgentName = agentName,
                    IsEnabled = contact.AutoReplyEnabled, // 保持当前自动回复状态不变
                    ReplyDelaySeconds = 1
                };

                var result = await WxApi.SetContactAiAgent(command);

                if (!result)
                {
                    throw new Exception("API调用失败");
                }

                // 3. 简化缓存清理 - 统一缓存失效
                await UnifiedDataManager.InvalidateCacheAsync(contact.WxManagerId);
                _tabDataCache.Remove(contact.WxManagerId);

                // 4. 强制从数据库重新加载数据，不使用缓存
                await OnSearchCore(useCache: false);

                // 5. 更新本地UI状态
                await InvokeAsync(() =>
                {
                    if (string.IsNullOrEmpty(agentName))
                    {
                        contactAiAgentMapping.Remove(contact.Id);
                    }
                    else
                    {
                        contactAiAgentMapping[contact.Id] = agentName;
                    }
                    StateHasChanged();
                });

                Logger.LogDebug("✅ 事务性AI配置更新成功");

                // 🚀 新增：发送实时配置变更通知
                await SendConfigurationChangeNotificationAsync("ContactAiConfig", new
                {
                    ContactId = contact.Id,
                    WcId = contact.WcId,
                    WxManagerId = contact.WxManagerId,
                    AgentName = agentName,
                    UpdatedAt = DateTime.UtcNow
                });

                return true;
            }
            catch (Exception ex)
            {
                retryCount++;
                Logger.LogWarning(ex, "⚠️ 事务性AI配置更新失败 - 尝试 {Retry}/{Max}", retryCount, maxRetries);

                if (retryCount >= maxRetries)
                {
                    Logger.LogError(ex, "❌ 事务性AI配置更新最终失败");
                    throw;
                }

                // 指数退避重试
                await Task.Delay(Math.Min(1000 * (int)Math.Pow(2, retryCount - 1), 5000));
            }
        }

        return false;
    }

    /// <summary>
    /// 使用标准化数据管理器的AI配置更新
    /// </summary>
    private async Task<bool> UpdateAiConfigWithStandardizedManagerAsync(
        WxContactVo contact,
        string agentName,
        string operation)
    {
        // 记录原始状态用于回滚
        var originalAgent = contactAiAgentMapping.GetValueOrDefault(contact.Id);

        // 定义操作函数
        async Task<bool> OperationFunc()
        {
            try
            {
                Logger.LogDebug("🔄 执行AI配置更新操作 - Contact: {ContactId}, Agent: {AgentName}",
                    contact.Id, agentName);

                // 1. 预检查：验证联系人状态
                if (contact.WxManagerId == Guid.Empty)
                {
                    throw new InvalidOperationException("联系人微信管理器ID无效");
                }

                // 2. 执行API调用
                var command = new Application.DTOs.Requests.Commands.SetContactAiAgentCommand
                {
                    ContactId = contact.Id,
                    WxManagerId = contact.WxManagerId,
                    AiAgentName = agentName,
                    IsEnabled = contact.AutoReplyEnabled,
                    ReplyDelaySeconds = 1
                };

                var result = await WxApi.SetContactAiAgent(command);

                if (!result)
                {
                    Logger.LogError("❌ API调用失败 - Contact: {ContactId}", contact.Id);
                    return false;
                }

                // 3. 清理缓存
                await UnifiedDataManager.InvalidateCacheAsync(contact.WxManagerId);
                _tabDataCache.Remove(contact.WxManagerId);

                // 🔧 修复：使用统一配置服务实现实时配置刷新
                try
                {
                    if (UnifiedConfigService != null)
                    {
                        await UnifiedConfigService.InvalidateConfigCacheAsync($"contact_ai_{contact.WxManagerId}_{contact.WcId}");
                        Logger.LogDebug("✅ AI配置缓存已强制刷新 - Contact: {ContactId}, WcId: {WcId}", contact.Id, contact.WcId);
                    }
                    else
                    {
                        Logger.LogDebug("✅ AI配置已更新，将在3秒内自动生效 - Contact: {ContactId}, WcId: {WcId}", contact.Id, contact.WcId);
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogWarning(ex, "强制刷新AI配置缓存失败，将在3秒内自动生效 - Contact: {ContactId}, WcId: {WcId}", contact.Id, contact.WcId);
                }

                // 4. 更新本地UI状态
                await InvokeAsync(() =>
                {
                    if (string.IsNullOrEmpty(agentName))
                    {
                        contactAiAgentMapping.Remove(contact.Id);
                    }
                    else
                    {
                        contactAiAgentMapping[contact.Id] = agentName;
                    }
                    StateHasChanged();
                });

                // 5. 重新加载数据验证
                await OnSearchCore(useCache: false);

                Logger.LogDebug("✅ AI配置更新操作成功");
                return true;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "❌ AI配置更新操作失败 - Contact: {ContactId}", contact.Id);
                return false;
            }
        }

        // 定义回滚函数
        async Task RollbackFunc()
        {
            try
            {
                Logger.LogWarning("🔙 执行AI配置回滚 - Contact: {ContactId}", contact.Id);

                // 恢复UI状态
                await InvokeAsync(() =>
                {
                    if (string.IsNullOrEmpty(originalAgent))
                    {
                        contactAiAgentMapping.Remove(contact.Id);
                    }
                    else
                    {
                        contactAiAgentMapping[contact.Id] = originalAgent;
                    }
                    StateHasChanged();
                });

                // 尝试恢复后端状态
                if (!string.IsNullOrEmpty(originalAgent))
                {
                    var rollbackCommand = new Application.DTOs.Requests.Commands.SetContactAiAgentCommand
                    {
                        ContactId = contact.Id,
                        WxManagerId = contact.WxManagerId,
                        AiAgentName = originalAgent,
                        IsEnabled = contact.AutoReplyEnabled,
                        ReplyDelaySeconds = 1
                    };

                    await WxApi.SetContactAiAgent(rollbackCommand);
                }

                Logger.LogInformation("✅ AI配置回滚完成");
            }
            catch (Exception rollbackEx)
            {
                Logger.LogError(rollbackEx, "❌ AI配置回滚失败 - Contact: {ContactId}", contact.Id);
            }
        }

        // 简化操作：直接执行操作函数
        try
        {
            var success = await OperationFunc();
            if (success)
            {
                Logger.LogInformation("✅ AI配置更新成功 - Contact: {ContactId}", contact.Id);
                return true;
            }
            else
            {
                Logger.LogError("❌ AI配置更新失败 - Contact: {ContactId}", contact.Id);
                // 执行回滚
                await RollbackFunc();
                throw new Exception("AI配置更新失败");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ AI配置更新异常 - Contact: {ContactId}", contact.Id);
            // 执行回滚
            try
            {
                await RollbackFunc();
            }
            catch (Exception rollbackEx)
            {
                Logger.LogError(rollbackEx, "❌ 回滚操作失败 - Contact: {ContactId}", contact.Id);
            }
            throw;
        }
    }

    // 获取状态颜色
    private Color GetStatusColor(Domain.ValueObjects.Enums.WxStatus status)
    {
        return status switch
        {
            Domain.ValueObjects.Enums.WxStatus.AlreadyLogIn => Color.Success,
            Domain.ValueObjects.Enums.WxStatus.NotLogIn => Color.Error,
            _ => Color.Warning
        };
    }

    // 获取状态文本
    private string GetStatusText(Domain.ValueObjects.Enums.WxStatus status)
    {
        return status switch
        {
            Domain.ValueObjects.Enums.WxStatus.AlreadyLogIn => "在线",
            Domain.ValueObjects.Enums.WxStatus.NotLogIn => "离线",
            _ => "未知"
        };
    }

    private string GetContactMode(WxContactVo contact)
    {
        return contact.AutoReplyEnabled ? "自动回复" : "人工回复";
    }

    private async Task OnModeChanged(WxContactVo contact, string mode)
    {
        try
        {
            var enabled = mode == "自动回复";

            // 友好提示：如果设置为自动回复但没有配置AI机器人
            if (enabled && !contactAiAgentMapping.ContainsKey(contact.Id))
            {
                Snackbar.Add($"提示：联系人 {contact.NickName} 未配置AI机器人，建议先配置AI机器人以获得更好的自动回复效果", Severity.Info);
            }

            // 独立设置自动回复状态，不影响其他配置项
            await OnAutoReplyToggle(contact, enabled);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"设置回复模式失败: {ex.Message}", Severity.Error);
            // 强制刷新数据恢复正确状态
            await OnSearchCore(useCache: false);
        }
    }



    // 对外的搜索接口，保持兼容性
    private async Task OnSearch()
    {
        await OnSearchCore();
    }

    // 核心搜索逻辑，支持缓存优化 - 增强错误诊断
    private async Task OnSearchCore(bool useCache = true)
    {
        // 🔍 增强诊断：详细检查账户状态
        await DiagnoseAccountStatus();

        if (!_hasEnabledAccounts)
        {
            Logger.LogWarning("⚠️ 没有可用的微信账户 - 总账户数: {TotalAccounts}, 已登录: {LoggedInCount}, 已启用: {EnabledCount}",
                wxAccounts?.Count ?? 0,
                wxAccounts?.Count(a => a.WxStatus == WxStatus.AlreadyLogIn) ?? 0,
                wxAccounts?.Count(a => a.WxStatus == WxStatus.AlreadyLogIn && a.IsEnabled) ?? 0);

            // 🔧 简化处理：直接返回，不显示弹窗
            return;
        }

        // 检查是否有选中的微信账户
        if (!HasSelectedAccount)
        {
            Logger.LogWarning("⚠️ 没有选中的微信账户 - ActiveTabIndex: {TabIndex}, AccountsCount: {Count}",
                _activeTabIndex, wxAccounts?.Count ?? 0);

            // 🔄 尝试重新加载账户信息
            await LoadWxAccountsAsync();
            StateHasChanged();

            if (!HasSelectedAccount)
            {
                await DialogService.ShowMessageBox("账户选择异常",
                    "无法获取当前选中的微信账户。请刷新页面或重新选择账户。");
                return;
            }
        }

        var loadType = useCache ? "缓存优先" : "无缓存";
        // 🔧 注释冗余的数据加载开始日志 - 减少日志噪音
        // Logger.LogInformation("🔍 开始联系人数据加载 - Account: {AccountName}, LoadType: {LoadType}", SelectedAccount!.NickName, loadType);

        _isLoading = true;

        // 立即更新UI显示加载状态
        await InvokeAsync(StateHasChanged);
        try
        {
            // 标准化搜索参数
            var searchNickName = string.IsNullOrWhiteSpace(_searchedNickName) ? "" : _searchedNickName.Trim();
            var searchRemarkName = string.IsNullOrWhiteSpace(_searchedRemarkName) ? "" : _searchedRemarkName.Trim();

            // 如果是搜索操作，重置到第一页
            if (!string.IsNullOrEmpty(searchNickName) && _page != 1)
            {
                _page = 1;
            }

            // 检查缓存（仅在无搜索条件且第一页时）
            var cacheKey = SelectedAccount.Id;
            PageResponse<WxContactDto>? resp = null;

            if (useCache && string.IsNullOrEmpty(searchNickName) && string.IsNullOrEmpty(searchRemarkName) && _page == 1)
            {
                _tabDataCache.TryGetValue(cacheKey, out resp);
                if (resp != null)
                {
                    Logger.LogDebug("💾 使用缓存的联系人数据 - Count: {Count}", resp.Items?.Count() ?? 0);
                }
            }

            // 🚀 简化数据加载：统一使用StandardizedDataManager，移除多层缓存
            if (resp == null)
            {
                Logger.LogDebug("🌐 从StandardizedDataManager获取联系人数据 - UseCache: {UseCache}", useCache);

                // 🔄 使用联系人查询优化器，简化数据获取逻辑 - 支持个人联系人和企业联系人
                var dtoResp = await ContactQueryOptimizer.GetOptimizedContactListAsync(
                    SelectedAccount.Id,
                    new List<WxContactType> { WxContactType.Contact, WxContactType.Enterprise },
                    searchNickName,
                    searchRemarkName,
                    _page,
                    _pageSize);

                // 转换为统一格式
                resp = new PageResponse<WxContactDto>
                {
                    Items = dtoResp.Items,
                    Page = dtoResp.Page,
                    PageSize = dtoResp.PageSize,
                    TotalCount = dtoResp.TotalCount
                };

                // 🗂️ 简化缓存策略：只缓存第一页无搜索条件的结果
                if (useCache && string.IsNullOrEmpty(searchNickName) && string.IsNullOrEmpty(searchRemarkName) && _page == 1 && resp != null)
                {
                    _tabDataCache[cacheKey] = resp;
                    Logger.LogDebug("💾 缓存联系人数据 - Count: {Count}", resp.Items?.Count() ?? 0);
                }
            }

            // 转换为VO对象
            Elements = resp?.Items?.Select(dto => new WxContactVo
            {
                Id = dto.Id,
                WxManagerId = dto.WxManagerId,
                WcId = dto.WcId,
                NickName = dto.NickName,
                Remark = dto.Remark, // 注意：DTO中是Remark，VO中也是Remark
                UserName = dto.UserName,
                Sex = dto.Sex,
                Province = dto.Province,
                City = dto.City,
                Signature = dto.Signature,
                ContactType = dto.ContactType,
                AliasName = dto.AliasName,
                Country = dto.Country,
                BigHead = dto.BigHead,
                SmallHead = dto.SmallHead,
                LabelList = dto.LabelList,
                V1 = dto.V1,
                AutoReplyEnabled = dto.AutoReplyEnabled ?? false
            }).ToList() ?? [];

            _totalPages = resp != null ? (int)Math.Ceiling((double)resp.TotalCount / resp.PageSize) : 0;

            // Logger.LogInformation("📊 联系人数据加载完成 - Elements: {Count}, TotalPages: {TotalPages}", Elements?.Count() ?? 0, _totalPages);

            // 更新UI显示数据
            await InvokeAsync(StateHasChanged);

            // 异步加载联系人的AI配置（不阻塞主界面）
            await LoadContactsAiConfigAsync();

            // 更新选择状态
            UpdateSelectAllState();

            // 🔧 确保重置同步按钮状态（数据加载完成后）
            if (_isSyncing)
            {
                _isSyncing = false;
                Logger.LogInformation("🔄 数据加载完成，重置联系人同步按钮状态");
            }

            // 再次强制UI更新以确保所有状态同步
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 联系人数据加载失败 - Account: {AccountName}, WxManagerId: {ManagerId}, UseCache: {UseCache}",
                SelectedAccount?.NickName ?? "未知", SelectedAccount?.Id, useCache);

            // 🔍 增强错误分析和用户指导
            var errorAnalysis = await AnalyzeDataLoadError(ex);

            if (ex.Message.Contains("微信未登录") || ex.Message.Contains("微信已注销") || ex.Message.Contains("会话已过期"))
            {
                // 重新加载微信账户状态
                await LoadWxAccountsAsync();
                StateHasChanged();

                // 显示详细的错误信息和解决方案
                await DialogService.ShowMessageBox("微信登录状态异常",
                    $"错误详情: {ex.Message}\n\n{errorAnalysis.SolutionSuggestion}");
                return;
            }

            // 🔄 尝试降级处理：绕过缓存重新加载
            if (useCache && !errorAnalysis.IsCriticalError)
            {
                Logger.LogInformation("🔄 尝试降级处理：绕过缓存重新加载数据");
                try
                {
                    await OnSearchCore(useCache: false);
                    return;
                }
                catch (Exception fallbackEx)
                {
                    Logger.LogError(fallbackEx, "❌ 降级处理也失败了");
                }
            }

            // 重置数据状态
            Elements = new List<WxContactVo>();
            _totalPages = 0;

            // 显示用户友好的错误信息
            await DialogService.ShowMessageBox("数据加载失败",
                $"{errorAnalysis.UserFriendlyMessage}\n\n{errorAnalysis.SolutionSuggestion}");
        }
        finally
        {
            _isLoading = false;

            // 🔧 确保同步状态也被重置（异常安全）
            if (_isSyncing)
            {
                _isSyncing = false;
                Logger.LogInformation("🔄 异常安全重置：联系人同步按钮状态已重置");
            }

            // 确保加载状态更新立即反映到UI
            await InvokeAsync(StateHasChanged);

            Logger.LogDebug("🏁 联系人数据加载流程结束 - isLoading: {IsLoading}, isSyncing: {IsSyncing}", _isLoading, _isSyncing);
        }
    }

    // 🔍 账户状态诊断方法
    private async Task DiagnoseAccountStatus()
    {
        try
        {
            // 🔧 注释冗余的账户状态诊断开始日志 - 减少日志噪音
            // Logger.LogInformation("🔍 开始账户状态诊断");

            // 检查账户加载状态
            if (wxAccounts == null)
            {
                Logger.LogWarning("⚠️ wxAccounts为null，尝试重新加载");
                await LoadWxAccountsAsync();
            }

            var totalAccounts = wxAccounts?.Count ?? 0;
            var loggedInAccounts = wxAccounts?.Where(a => a.WxStatus == WxStatus.AlreadyLogIn).ToList() ?? new List<WxManagerVo>();
            var enabledAccounts = loggedInAccounts.Where(a => a.IsEnabled).ToList();

            // 🔧 注释冗余的账户状态统计日志 - 减少日志噪音
            // Logger.LogInformation("📊 账户状态统计 - 总数: {Total}, 已登录: {LoggedIn}, 已启用: {Enabled}",
            //     totalAccounts, loggedInAccounts.Count, enabledAccounts.Count);

            // 详细记录每个账户的状态
            if (wxAccounts != null)
            {
                foreach (var account in wxAccounts)
                {
                    Logger.LogDebug("👤 账户详情 - ID: {Id}, 昵称: {NickName}, 状态: {Status}, 启用: {Enabled}",
                        account.Id, account.NickName, account.WxStatus, account.IsEnabled);
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 账户状态诊断失败");
        }
    }



    // 🔍 数据加载错误分析
    private async Task<ErrorAnalysisResult> AnalyzeDataLoadError(Exception ex)
    {
        var result = new ErrorAnalysisResult();

        try
        {
            // 分析错误类型
            if (ex is TimeoutException || ex.Message.Contains("timeout"))
            {
                result.UserFriendlyMessage = "数据加载超时，可能是网络或数据库响应缓慢";
                result.SolutionSuggestion = "请稍后重试，或联系管理员检查系统状态";
                result.IsCriticalError = false;
            }
            else if (ex.Message.Contains("数据库") || ex.Message.Contains("connection"))
            {
                result.UserFriendlyMessage = "数据库连接异常";
                result.SolutionSuggestion = "请检查网络连接，或联系管理员";
                result.IsCriticalError = true;
            }
            else if (ex.Message.Contains("缓存") || ex.Message.Contains("Redis"))
            {
                result.UserFriendlyMessage = "缓存服务异常，已尝试直接从数据库加载";
                result.SolutionSuggestion = "如果问题持续，请刷新页面重试";
                result.IsCriticalError = false;
            }
            else
            {
                result.UserFriendlyMessage = "数据加载时发生未知错误";
                result.SolutionSuggestion = "请刷新页面重试，如果问题持续请联系管理员";
                result.IsCriticalError = false;
            }

            // 记录详细的错误信息用于调试
            Logger.LogError("🔍 错误分析结果 - 类型: {ErrorType}, 消息: {Message}, 关键错误: {IsCritical}",
                ex.GetType().Name, result.UserFriendlyMessage, result.IsCriticalError);

        }
        catch (Exception analysisEx)
        {
            Logger.LogError(analysisEx, "❌ 错误分析过程中发生异常");
            result.UserFriendlyMessage = "系统发生异常";
            result.SolutionSuggestion = "请刷新页面重试";
            result.IsCriticalError = true;
        }

        return await Task.FromResult(result);
    }

    // 错误分析结果类
    private class ErrorAnalysisResult
    {
        public string UserFriendlyMessage { get; set; } = "";
        public string SolutionSuggestion { get; set; } = "";
        public bool IsCriticalError { get; set; } = false;
    }

    private async Task SendMessage(WxContactVo contactVo)
    {
        // 🔧 验证当前选中的微信账号
        if (!HasSelectedAccount)
        {
            await DialogService.ShowMessageBox("提示", "请先选择微信账号");
            return;
        }

        var parameters = new DialogParameters()
        {
            {
                "WxContactVo", contactVo
            },
            {
                "CurrentWxManager", SelectedAccount
            }
        };
        await DialogService.ShowAsync<WxSendMessage>("发送消息", parameters);
    }

    // 🔄 强制重新加载数据（用于故障恢复）
    private async Task ForceReloadData()
    {
        try
        {
            Logger.LogInformation("🔄 强制重新加载联系人数据");

            // 重置同步状态
            _isSyncing = false;

            // 清除所有缓存
            _tabDataCache.Clear();

            // 重新加载账户信息
            await LoadWxAccountsAsync();

            // 强制从数据库加载数据
            await OnSearchCore(useCache: false);

            Snackbar.Add("数据已重新加载，同步状态已重置", Severity.Success);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 强制重新加载数据失败");
            Snackbar.Add($"重新加载失败: {ex.Message}", Severity.Error);
        }
    }

    // 🔧 数据状态检查工具 - 增强版，包含数据库检查
    private async Task CheckDataStatus()
    {
        try
        {
            var statusInfo = new List<string>();

            // 检查账户状态
            statusInfo.Add($"微信账户总数: {wxAccounts?.Count ?? 0}");
            statusInfo.Add($"已登录账户: {wxAccounts?.Count(a => a.WxStatus == WxStatus.AlreadyLogIn) ?? 0}");
            statusInfo.Add($"已启用账户: {wxAccounts?.Count(a => a.WxStatus == WxStatus.AlreadyLogIn && a.IsEnabled) ?? 0}");
            statusInfo.Add("");

            // 检查当前选中账户
            if (HasSelectedAccount)
            {
                var account = SelectedAccount!;
                statusInfo.Add($"当前账户: {account.NickName} (ID: {account.Id})");
                statusInfo.Add($"账户状态: {account.WxStatus}");
                statusInfo.Add($"是否启用: {account.IsEnabled}");
                statusInfo.Add($"WcId: {account.WcId}");
                statusInfo.Add("");

                // 🔍 检查数据库中的实际数据
                try
                {
                    Logger.LogInformation("🔍 开始检查数据库中的联系人数据");

                    var query = new GetContactOrGroupListQuery
                    {
                        WxManagerId = account.Id,
                        ContactTypes = new List<WxContactType> { WxContactType.Contact, WxContactType.Enterprise },
                        SearchedNickName = "",
                        SearchedRemarkName = "",
                        PageQuery = new PageQuery { Page = 1, PageSize = 100 }
                    };

                    var dbResult = await WxApi.getContactOrGroupList(query);
                    statusInfo.Add("📊 数据库查询结果:");
                    statusInfo.Add($"  - 总数: {dbResult?.TotalCount ?? 0}");
                    statusInfo.Add($"  - 返回条数: {dbResult?.Items?.Count() ?? 0}");
                    statusInfo.Add($"  - 总页数: {Math.Ceiling((dbResult?.TotalCount ?? 0) / 10.0)}");

                    if (dbResult?.Items?.Any() == true)
                    {
                        var firstContact = dbResult.Items.First();
                        statusInfo.Add($"  - 示例联系人: {firstContact.NickName} ({firstContact.UserName})");
                        statusInfo.Add($"  - 联系人类型: {firstContact.ContactType}");
                    }
                    else
                    {
                        statusInfo.Add("  - ⚠️ 数据库中没有找到联系人数据");
                        statusInfo.Add("  - 可能原因:");
                        statusInfo.Add("    1) 联系人未同步");
                        statusInfo.Add("    2) 数据被清空");
                        statusInfo.Add("    3) 查询条件有误");
                        statusInfo.Add("    4) 数据库连接问题");
                    }
                }
                catch (Exception dbEx)
                {
                    statusInfo.Add($"❌ 数据库查询失败: {dbEx.Message}");
                    Logger.LogError(dbEx, "数据库查询失败");
                }
            }
            else
            {
                statusInfo.Add("当前账户: 无");
            }

            statusInfo.Add("");
            // 检查前端数据状态
            statusInfo.Add("🖥️ 前端数据状态:");
            statusInfo.Add($"  - 联系人数量: {Elements?.Count() ?? 0}");
            statusInfo.Add($"  - 缓存数量: {_tabDataCache.Count}");
            statusInfo.Add($"  - 当前页: {_page}");
            statusInfo.Add($"  - 总页数: {_totalPages}");
            statusInfo.Add($"  - 加载状态: {(_isLoading ? "加载中" : "空闲")}");

            var message = string.Join("\n", statusInfo);
            await DialogService.ShowMessageBox("数据状态检查", message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 数据状态检查失败");
            await DialogService.ShowMessageBox("检查失败", $"无法获取数据状态: {ex.Message}");
        }
    }

    private async Task OnPageChanged(int newPage)
    {
        _page = newPage;
        await OnSearch();

    }

    private async Task OnAutoReplyToggle(WxContactVo contactVo, bool enabled)
    {
        try
        {
            // 🔧 注释冗余的自动回复设置开始日志 - 减少日志噪音
            // Logger.LogInformation("开始独立设置联系人自动回复状态 - ContactId: {ContactId}, Enabled: {Enabled}",
            //     contactVo.Id, enabled);

            await WxApi.SetAutoReplyStatus(new Application.DTOs.Requests.Commands.SetAutoReplyStatusDto
            {
                ContactId = contactVo.Id,
                Enabled = enabled
            });

            // 简化缓存清理
            await UnifiedDataManager.InvalidateCacheAsync(contactVo.WxManagerId);
            _tabDataCache.Remove(contactVo.WxManagerId);

            // 强制从数据库重新加载数据，不使用缓存
            await OnSearchCore(useCache: false);

            // 短暂延迟确保后端缓存清理完成
            await Task.Delay(100);

            // 强制重新加载AI配置，确保前端AI配置与自动回复状态同步
            await LoadContactsAiConfigWithRetryAsync();

            // 更新本地UI状态
            await InvokeAsync(() =>
            {
                contactVo.AutoReplyEnabled = enabled;
                StateHasChanged();
            });

            Snackbar.Add($"自动回复已{(enabled ? "开启" : "关闭")}", Severity.Success);

            // 🔧 注释冗余的自动回复设置完成日志 - 减少日志噪音
            // Logger.LogInformation("联系人自动回复状态设置完成 - ContactId: {ContactId}, Enabled: {Enabled}",
            //     contactVo.Id, enabled);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"设置自动回复状态失败: {ex.Message}", Severity.Error);
            Logger.LogError(ex, "设置联系人自动回复状态失败 - ContactId: {ContactId}", contactVo.Id);

            // 强制刷新数据恢复正确状态
            await OnSearchCore(useCache: false);
        }
    }

    /// <summary>
    /// 独立配置系统：增强缓存同步 - 确保所有相关缓存被正确清理
    /// </summary>
    private async Task InvalidateAllConfigCachesAsync(Guid wxManagerId, string configType)
    {
        try
        {
            Logger.LogDebug("🧹 开始增强缓存清理 - ConfigType: {ConfigType}, WxManagerId: {WxManagerId}", configType, wxManagerId);

            // 统一配置管理器缓存清理
            await UnifiedConfigManager.ClearConfigCacheAsync("WxContact");
            await UnifiedConfigManager.ClearConfigCacheAsync("ContactAiConfig");
            await UnifiedConfigManager.ClearConfigCacheAsync("AiAgent");
            await UnifiedConfigManager.ClearConfigCacheAsync(configType);

            // 数据管理器缓存清理
            await UnifiedDataManager.InvalidateCacheAsync(wxManagerId);

            // 特定于配置类型的额外清理
            switch (configType.ToLowerInvariant())
            {
                case "aiagent":
                    await UnifiedConfigManager.ClearConfigCacheAsync("AiConfig");
                    break;
                case "autoreply":
                    await UnifiedConfigManager.ClearConfigCacheAsync("AutoReplyConfig");
                    break;
            }

            // 强制等待缓存同步
            await Task.Delay(300);

            Logger.LogDebug("✅ 增强缓存清理完成 - ConfigType: {ConfigType}", configType);
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "⚠️ 增强缓存清理部分失败 - ConfigType: {ConfigType}", configType);
        }
    }

    private async Task DeleteContact(WxContactVo contactVo)
    {
        // 创建详细的删除确认对话框
        var confirmationContent = $"确定要删除联系人吗？\n\n联系人信息：\n• 昵称：{contactVo.NickName}\n• 微信号：{contactVo.AliasName}\n• 备注：{contactVo.Remark ?? "无"}\n\n注意：此操作不可撤销！";

        bool? result = await DialogService.ShowMessageBox(
            "确认删除联系人",
            confirmationContent,
            yesText: "确认删除",
            cancelText: "取消");

        if (result == true)
        {
            // 🛡️ 防止重复删除：检查是否已在删除中
            if (_deletingContactIds.Contains(contactVo.Id))
            {
                Snackbar.Add($"联系人 {contactVo.NickName} 正在删除中，请稍候...", Severity.Warning);
                return;
            }

            // 设置删除状态
            _deletingContactIds.Add(contactVo.Id);
            StateHasChanged();

            try
            {
                // 显示删除进度
                Snackbar.Add($"正在删除联系人 {contactVo.NickName}...", Severity.Info);

                // 🔥 修复：先调用真正的删除API
                // 🔧 注释冗余的删除开始日志 - 减少日志噪音，Controller层已有Warning级别日志
                // Logger.LogInformation("🚀 开始执行联系人删除 - ContactId: {ContactId}, NickName: {NickName}",
                //     contactVo.Id, contactVo.NickName);

                await WxApi.DeleteContact(contactVo.Id);
                // 🔧 注释冗余的API调用成功日志 - 减少日志噪音
                // Logger.LogInformation("✅ API删除调用成功 - ContactId: {ContactId}", contactVo.Id);

                // 🚀 删除成功后，清理缓存
                var deleteResult = await UnifiedDataManager.ClearCacheAsync(SelectedAccount!.Id);

                if (deleteResult)
                {
                    // ✨ 使用统一前端缓存服务进行智能缓存清理
                    var cacheResult = await UnifiedFrontendCacheService.SmartClearAsync(
                        SelectedAccount!.Id,
                        FrontendCacheOperationType.SingleDelete,
                        new List<Guid> { contactVo.Id });

                    if (cacheResult.Success)
                    {
                        Logger.LogDebug("✅ 单个删除缓存清理成功 - ContactId: {ContactId}, 耗时: {ElapsedMs}ms",
                            contactVo.Id, cacheResult.ElapsedMs);
                    }

                    // 强制重新加载数据以确保一致性
                    await OnSearchCore(useCache: false);

                    // 🔍 验证删除操作是否真正生效
                    var stillExists = Elements.Any(c => c.Id == contactVo.Id);
                    if (stillExists)
                    {
                        Logger.LogWarning("⚠️ 数据一致性问题：删除后联系人仍存在 - ContactId: {ContactId}", contactVo.Id);
                        Snackbar.Add($"删除联系人可能未完全生效，请刷新页面确认", Severity.Warning);

                        // 尝试再次刷新数据
                        await Task.Delay(500);
                        await OnSearchCore(useCache: false);

                        // 再次检查
                        stillExists = Elements.Any(c => c.Id == contactVo.Id);
                        if (stillExists)
                        {
                            Logger.LogError("❌ 严重的数据一致性问题：删除操作未生效 - ContactId: {ContactId}", contactVo.Id);
                            Snackbar.Add($"删除联系人失败：数据库操作可能未成功", Severity.Error);
                            return; // 不执行后续的成功处理
                        }
                    }

                    // 移除选择状态
                    _selectedContactIds.Remove(contactVo.Id);
                    UpdateSelectAllState();

                    // 强制UI更新
                    await InvokeAsync(StateHasChanged);

                    Snackbar.Add($"成功删除联系人 {contactVo.NickName}", Severity.Success);
                    // 🔧 注释冗余的删除完成日志 - 减少日志噪音
                    // Logger.LogInformation("✅ 联系人删除完成 - ContactId: {ContactId}", contactVo.Id);
                }
                else
                {
                    Snackbar.Add($"删除联系人失败: 缓存清理失败", Severity.Error);
                    Logger.LogError("❌ 联系人缓存清理失败 - ContactId: {ContactId}", contactVo.Id);
                }

            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "❌ 联系人删除异常 - ContactId: {ContactId}", contactVo.Id);
                Snackbar.Add($"删除联系人失败: {ex.Message}", Severity.Error);
            }
            finally
            {
                // 🧹 确保删除状态被清理
                if (_deletingContactIds.Contains(contactVo.Id))
                {
                    _deletingContactIds.Remove(contactVo.Id);
                    StateHasChanged();
                }
            }
        }
    }

    protected void NavigateToWxManage()
    {
        Navigation.NavigateTo("/wxManage");
    }



    /// <summary>
    /// 验证删除后的数据一致性
    /// </summary>
    private async Task ValidateDataConsistencyAfterDelete(Guid deletedContactId)
    {
        try
        {
            // 短暂延迟确保后端处理完成
            await Task.Delay(200);

            // 验证联系人是否真的从列表中移除
            var stillExists = Elements.Any(c => c.Id == deletedContactId);
            if (stillExists)
            {
                Logger.LogWarning("⚠️ 数据一致性问题：已删除的联系人仍在列表中 - ContactId: {ContactId}", deletedContactId);

                // 强制再次刷新
                await OnSearchCore(useCache: false);

                // 如果仍然存在，记录错误
                if (Elements.Any(c => c.Id == deletedContactId))
                {
                    Logger.LogError("❌ 严重的数据一致性问题：删除操作未生效 - ContactId: {ContactId}", deletedContactId);
                    Snackbar.Add("数据同步异常，请刷新页面", Severity.Warning);
                }
            }
            else
            {
                Logger.LogDebug("✅ 删除后数据一致性验证通过 - ContactId: {ContactId}", deletedContactId);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 删除后数据一致性验证失败 - ContactId: {ContactId}", deletedContactId);
        }
    }

    /// <summary>
    /// 确保删除操作完全生效 - 增强版数据同步机制
    /// </summary>
    private async Task EnsureDeleteOperationCompleted(Guid deletedContactId, string contactName)
    {
        const int maxRetries = 5;
        const int baseDelayMs = 300;

        try
        {
            Logger.LogInformation("🔍 开始验证删除操作完成状态 - ContactId: {ContactId}", deletedContactId);

            for (var attempt = 1; attempt <= maxRetries; attempt++)
            {
                // 渐进式延迟：第一次300ms，第二次600ms，第三次900ms...
                var delayMs = baseDelayMs * attempt;
                await Task.Delay(delayMs);

                Logger.LogDebug("🔄 第 {Attempt}/{MaxRetries} 次验证删除状态 - ContactId: {ContactId}",
                    attempt, maxRetries, deletedContactId);

                // 强制从数据库重新加载，绕过所有缓存层
                await OnSearchCore(useCache: false);

                // 验证联系人是否已从列表中移除
                var stillExists = Elements.Any(c => c.Id == deletedContactId);

                if (!stillExists)
                {
                    // 删除成功
                    Logger.LogInformation("✅ 删除操作验证成功 - ContactId: {ContactId}, 尝试次数: {Attempt}",
                        deletedContactId, attempt);
                    Snackbar.Add($"成功删除联系人 {contactName}", Severity.Success);
                    return;
                }

                if (attempt == maxRetries)
                {
                    // 最后一次尝试仍然失败
                    Logger.LogError("❌ 严重的数据一致性问题：删除操作未生效 - ContactId: {ContactId}, 已尝试 {Attempts} 次",
                        deletedContactId, maxRetries);
                    Snackbar.Add($"删除联系人 {contactName} 可能未完全生效，请刷新页面确认", Severity.Warning);

                    // 尝试最后的修复措施：清除所有缓存并强制刷新
                    await ForceFullCacheRefresh();
                }
                else
                {
                    Logger.LogWarning("⚠️ 第 {Attempt} 次验证失败，联系人仍存在，将重试 - ContactId: {ContactId}",
                        attempt, deletedContactId);
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 删除操作验证过程异常 - ContactId: {ContactId}", deletedContactId);
            Snackbar.Add($"删除联系人 {contactName} 的验证过程出现异常", Severity.Error);
        }
    }

    /// <summary>
    /// 清除联系人删除相关的所有缓存
    /// </summary>
    private async Task ClearAllCachesForContact(Guid contactId)
    {
        try
        {
            Logger.LogInformation("🧹 开始清除联系人删除相关的所有缓存 - ContactId: {ContactId}", contactId);

            if (HasSelectedAccount)
            {
                // 1. 清除前端本地缓存
                _tabDataCache.Clear();
                Logger.LogDebug("✅ 已清除前端本地缓存");

                // 2. 清除UnifiedDataManager缓存
                await UnifiedDataManager.InvalidateCacheAsync(SelectedAccount!.Id);
                Logger.LogDebug("✅ 已清除UnifiedDataManager缓存");

                // 3. 清除统一缓存管理器的缓存
                var pattern = $"*{SelectedAccount!.Id}*";
                var refreshResult = await UnifiedCacheManager.RemoveByPatternAsync(pattern);
                Logger.LogDebug("✅ 已清除统一缓存管理器缓存 - 成功: {Success}", refreshResult);

                // 4. 短暂延迟确保缓存清理完成
                await Task.Delay(100);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 清除联系人缓存失败 - ContactId: {ContactId}", contactId);
        }
    }

    /// <summary>
    /// 强制全面缓存刷新 - 使用统一前端缓存服务
    /// </summary>
    private async Task ForceFullCacheRefresh()
    {
        try
        {
            Logger.LogInformation("🔧 执行统一前端缓存刷新");

            if (HasSelectedAccount)
            {
                // 使用统一前端缓存服务进行全面缓存清理
                var result = await UnifiedFrontendCacheService.ForceRefreshAllAsync(
                    SelectedAccount!.Id,
                    FrontendCacheOperationType.ErrorRecovery);

                if (result.Success)
                {
                    Logger.LogInformation("✅ 统一前端缓存刷新成功 - 清理层级: {ClearedLayers}, 耗时: {ElapsedMs}ms",
                        result.ClearedLayers, result.ElapsedMs);

                    // 最终数据重新加载
                    await OnSearchCore(useCache: false);
                }
                else
                {
                    Logger.LogWarning("⚠️ 统一前端缓存刷新失败: {ErrorMessage}", result.ErrorMessage);

                    // 降级到原有方案
                    _tabDataCache.Clear();
                    var pattern = $"*{SelectedAccount!.Id}*";
                    var fallbackResult = await UnifiedCacheManager.RemoveByPatternAsync(pattern);
                    await OnSearchCore(useCache: false);

                    Logger.LogInformation("🔄 已执行降级缓存刷新方案 - 结果: {Result}", fallbackResult);
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 统一前端缓存刷新失败");
        }
    }

    /// <summary>
    /// 同步完成后强制刷新数据
    /// </summary>
    // 🚀 简化：删除超时检测定时器字段









    // 搜索联系人方法
    private async Task SearchContacts()
    {
        await ExecuteSearchImmediately();
    }

    // 立即执行搜索（用于按钮触发）
    private async Task ExecuteSearchImmediately()
    {
        _isSearching = true;
        StateHasChanged();

        try
        {
            _page = 1;
            await OnSearch();
        }
        finally
        {
            _isSearching = false;
            StateHasChanged();
        }
    }

    // 优化的Tab切换处理方法
    private async Task OnTabChanged(int newIndex)
    {
        try
        {
            _activeTabIndex = newIndex;

            // 调用基类的Tab切换处理
            await OnTabChangedAsync();

            // 联系人页面特有的Tab切换逻辑
            if (HasSelectedAccount)
            {
                // 清空选择状态
                _selectedContactIds.Clear();
                UpdateSelectAllState();

                // 重置分页，但保持搜索条件
                _page = 1;

                // 加入新的SignalR组
                try
                {
                    await JSRuntime.InvokeVoidAsync("joinWxManagerGroup", SelectedAccount!.Id.ToString());
                    Logger.LogInformation("🔗 已加入新的微信管理器组 - WxManagerId: {WxManagerId}", SelectedAccount.Id);
                }
                catch (Exception ex)
                {
                    Logger.LogWarning(ex, "⚠️ 加入微信管理器组失败");
                }

                Logger.LogInformation("📋 联系人Tab切换完成 - Account: {AccountName}", SelectedAccount!.NickName);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "联系人Tab切换失败");
            Snackbar.Add("页面切换失败，请稍后重试", Severity.Warning);
        }
    }

    // 多选相关方法
    private void OnContactSelectionChanged(Guid contactId, bool isSelected)
    {
        if (isSelected)
        {
            _selectedContactIds.Add(contactId);
        }
        else
        {
            _selectedContactIds.Remove(contactId);
        }

        UpdateSelectAllState();
        StateHasChanged();
    }

    private void OnSelectAllChanged(bool? value)
    {
        if (value == true)
        {
            // 全选当前页面的所有联系人
            _selectedContactIds.Clear();
            foreach (var contact in Elements)
            {
                _selectedContactIds.Add(contact.Id);
            }
        }
        else
        {
            // 清空选择
            _selectedContactIds.Clear();
        }

        UpdateSelectAllState();
        StateHasChanged();
    }

    private void UpdateSelectAllState()
    {
        var currentPageContactIds = Elements.Select(c => c.Id).ToHashSet();
        var selectedOnCurrentPage = _selectedContactIds.Where(id => currentPageContactIds.Contains(id)).Count();
        var totalOnCurrentPage = currentPageContactIds.Count;

        if (selectedOnCurrentPage == 0)
        {
            _isAllSelectedValue = false;
            _isIndeterminate = false;
        }
        else if (selectedOnCurrentPage == totalOnCurrentPage)
        {
            _isAllSelectedValue = true;
            _isIndeterminate = false;
        }
        else
        {
            _isAllSelectedValue = false;
            _isIndeterminate = true;
        }
    }

    private void ClearSelection()
    {
        _selectedContactIds.Clear();
        UpdateSelectAllState();
        StateHasChanged();
    }

    // 获取选中的联系人
    private object GetSelectedContacts()
    {
        return Elements.Where(c => _selectedContactIds.Contains(c.Id)).ToList();
    }



    // 批量删除确认方法
    private async Task<bool> ConfirmBatchDeleteAsync(object contactsObj)
    {
        var contactsList = contactsObj as IEnumerable;
        if (contactsList == null)
        {
            return false;
        }

        var contactCount = 0;
        var contactNamesList = new List<string>();

        foreach (var item in contactsList)
        {
            if (item is WxContactVo contact)
            {
                contactCount++;
                contactNamesList.Add(contact.NickName);
            }
        }

        if (contactCount == 0)
        {
            await DialogService.ShowMessageBox("提示", "请先选择要删除的联系人", yesText: "确定");
            return false;
        }

        var displayNames = contactNamesList.Take(5).Select(name => $"• {name}");
        var displayText = string.Join("\n", displayNames);
        if (contactCount > 5)
        {
            displayText += $"\n• ... 等其他 {contactCount - 5} 个联系人";
        }

        var confirmationContent = $"确定要批量删除 {contactCount} 个联系人吗？\n\n将要删除的联系人：\n{displayText}\n\n注意：此操作不可撤销！";

        bool? result = await DialogService.ShowMessageBox(
            "确认批量删除",
            confirmationContent,
            yesText: "确认删除",
            cancelText: "取消");

        return result == true;
    }



    /// <summary>
    /// 强制从数据库重新加载配置，忽略缓存
    /// </summary>
    private async Task ForceReloadConfigurations()
    {
        try
        {
            Logger.LogInformation("开始强制重新加载联系人配置");

            // 等待一小段时间确保数据库写入完成
            await Task.Delay(200);

            // 强制清理缓存并重新加载配置
            await UnifiedConfigManager.ClearConfigCacheAsync("WxContact");
            await UnifiedConfigManager.ClearConfigCacheAsync("AiAgent");
            await UnifiedConfigManager.ClearConfigCacheAsync("AutoReply");
            Logger.LogInformation("已强制清理所有相关缓存");

            // 清理本地缓存
            foreach (var managerId in _tabDataCache.Keys.ToList())
            {
                _tabDataCache.Remove(managerId);
            }
            await UnifiedDataManager.InvalidateCacheAsync(SelectedAccount.Id);

            // 重新加载所有数据
            await OnSearch();
            await LoadContactsAiConfigAsync();

            Logger.LogInformation("强制重新加载联系人配置完成");
        }
        catch (Exception ex)
        {
            Snackbar.Add($"强制重新加载配置失败: {ex.Message}", Severity.Error);
            Logger.LogError(ex, "强制重新加载联系人配置失败");
        }
        finally
        {
            StateHasChanged();
        }
    }

    protected async Task<bool> JoinWxManagerGroupAsync(Guid wxManagerId)
    {
        const int maxRetries = 3;
        const int baseDelayMs = 1000;
        const int timeoutMs = 15000;
        
        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                Logger.LogDebug("🔄 尝试加入SignalR组 - ManagerId: {ManagerId}, 尝试次数: {Attempt}/{MaxRetries}", 
                    wxManagerId, attempt, maxRetries);

                // 1. 检查连接状态
                var connectionStatus = await JSRuntime.InvokeAsync<object>("checkSignalRConnectionStatus");
                Logger.LogDebug("📡 SignalR连接状态检查: {ConnectionStatus}", connectionStatus);

                // 2. 带超时的组加入尝试
                var success = await InvokeWithTimeoutAsync(
                    () => JSRuntime.InvokeAsync<bool>("joinWxManagerGroup", wxManagerId.ToString()).AsTask(),
                    timeoutMs);

                if (success)
                {
                    // 3. 验证组成员身份（延迟验证以确保服务器端处理完成）
                    await Task.Delay(500);
                    var membershipVerified = await VerifyGroupMembershipAsync(wxManagerId);
                    
                    if (membershipVerified)
                    {
                        Logger.LogInformation("✅ 成功加入并验证SignalR组 - ManagerId: {ManagerId}, 尝试次数: {Attempt}", 
                            wxManagerId, attempt);
                        return true;
                    }
                    else
                    {
                        Logger.LogWarning("⚠️ 加入组成功但验证失败 - ManagerId: {ManagerId}, 尝试次数: {Attempt}", 
                            wxManagerId, attempt);
                    }
                }
                else
                {
                    Logger.LogWarning("❌ 加入SignalR组失败 - ManagerId: {ManagerId}, 尝试次数: {Attempt}", 
                        wxManagerId, attempt);
                }

                // 4. 如果不是最后一次尝试，则等待后重试
                if (attempt < maxRetries)
                {
                    var delay = baseDelayMs * attempt; // 递增延迟
                    Logger.LogDebug("⏳ 等待 {Delay}ms 后重试...", delay);
                    await Task.Delay(delay);
                    
                    // 5. 重试前尝试重新初始化连接
                    await JSRuntime.InvokeVoidAsync("reinitializeSignalRConnection");
                }
            }
            catch (TaskTimeoutException)
            {
                Logger.LogWarning("⏰ 加入SignalR组超时 - ManagerId: {ManagerId}, 尝试次数: {Attempt}, 超时时间: {TimeoutMs}ms", 
                    wxManagerId, attempt, timeoutMs);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "❌ 加入SignalR组异常 - ManagerId: {ManagerId}, 尝试次数: {Attempt}", 
                    wxManagerId, attempt);
            }
        }

        Logger.LogError("💥 所有重试尝试失败，无法加入SignalR组 - ManagerId: {ManagerId}", wxManagerId);
        
        // 6. 显示用户友好的错误提示
        await InvokeAsync(() => {
            Snackbar.Add("无法建立实时通知连接，可能无法及时收到同步完成通知", Severity.Warning);
        });
        
        return false;
    }

    /// <summary>
    /// 验证用户是否已成功加入指定的WxManager组
    /// </summary>
    private async Task<bool> VerifyGroupMembershipAsync(Guid wxManagerId)
    {
        try
        {
            var diagnosticResult = await JSRuntime.InvokeAsync<JsonElement>("diagnoseSignalRConnection");
            Logger.LogDebug("🔍 组成员身份验证诊断结果: {DiagnosticResult}", diagnosticResult);
            
            // 检查整体连接状态
            if (diagnosticResult.TryGetProperty("overallStatus", out var overallStatus))
            {
                var status = overallStatus.GetString();
                if (status != "connected")
                {
                    Logger.LogWarning("⚠️ SignalR整体连接状态异常: {Status}", status);
                    return false;
                }
            }
            
            // 检查服务器端诊断结果中的组成员身份
            if (diagnosticResult.TryGetProperty("serverDiagnostic", out var serverDiagnostic))
            {
                if (serverDiagnostic.TryGetProperty("IsInGroup", out var isInGroupElement))
                {
                    var isInGroup = isInGroupElement.GetBoolean();
                    Logger.LogDebug("📋 服务器端组成员身份验证结果: {IsInGroup} - ManagerId: {ManagerId}", 
                        isInGroup, wxManagerId);
                    return isInGroup;
                }
                
                // 检查是否有WxManagerId匹配
                if (serverDiagnostic.TryGetProperty("WxManagerId", out var serverWxManagerId))
                {
                    var serverManagerIdString = serverWxManagerId.GetString();
                    if (Guid.TryParse(serverManagerIdString, out var serverManagerId))
                    {
                        var isMatching = serverManagerId == wxManagerId;
                        Logger.LogDebug("🆔 WxManagerId匹配验证: {IsMatching} (服务器: {ServerManagerId}, 期望: {ExpectedManagerId})", 
                            isMatching, serverManagerId, wxManagerId);
                        return isMatching;
                    }
                }
            }
            
            // 如果没有服务器端诊断结果，检查客户端连接状态
            var hasActiveConnection = false;
            
            // 检查简化通知处理器
            if (diagnosticResult.TryGetProperty("simplifiedHandler", out var simplifiedHandler))
            {
                if (simplifiedHandler.TryGetProperty("isConnected", out var isConnectedElement))
                {
                    hasActiveConnection = isConnectedElement.GetBoolean();
                    Logger.LogDebug("📡 简化通知处理器连接状态: {IsConnected}", hasActiveConnection);
                }
            }
            
            // 检查contactSyncClient
            if (!hasActiveConnection && diagnosticResult.TryGetProperty("contactSyncClient", out var contactSyncClient))
            {
                if (contactSyncClient.TryGetProperty("isConnected", out var isConnectedElement))
                {
                    hasActiveConnection = isConnectedElement.GetBoolean();
                    Logger.LogDebug("📡 contactSyncClient连接状态: {IsConnected}", hasActiveConnection);
                }
            }
            
            Logger.LogDebug("🔍 组成员身份验证完成 - HasActiveConnection: {HasActiveConnection}, ManagerId: {ManagerId}", 
                hasActiveConnection, wxManagerId);
            
            return hasActiveConnection; // 如果有活跃连接，认为组加入成功
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "⚠️ 验证组成员身份失败 - ManagerId: {ManagerId}", wxManagerId);
            return false;
        }
    }

    /// <summary>
    /// 带超时执行异步任务
    /// </summary>
    private async Task<T> InvokeWithTimeoutAsync<T>(Func<Task<T>> taskFactory, int timeoutMs)
    {
        using var cts = new CancellationTokenSource(timeoutMs);
        
        try
        {
            var task = taskFactory();
            var completedTask = await Task.WhenAny(task, Task.Delay(timeoutMs, cts.Token));
            
            if (completedTask == task)
            {
                return await task;
            }
            else
            {
                throw new TaskTimeoutException($"操作在 {timeoutMs}ms 内未完成");
            }
        }
        catch (OperationCanceledException) when (cts.Token.IsCancellationRequested)
        {
            throw new TaskTimeoutException($"操作超时 ({timeoutMs}ms)");
        }
    }

    /// <summary>
    /// 自定义超时异常
    /// </summary>
    public class TaskTimeoutException : TimeoutException
    {
        public TaskTimeoutException(string message) : base(message) { }
    }

    /// <summary>
    /// 注册统一通知处理器回调
    /// </summary>
    private async Task RegisterUnifiedNotificationCallbacks()
    {
        try
        {
            // 检查Circuit状态
            if (!await IsCircuitHealthyAsync())
            {
                Logger.LogDebug("Circuit不健康，跳过回调注册");
                return;
            }
            // 注册联系人同步完成回调
            await JSRuntime.InvokeVoidAsync("eval", @"
                if (window.unifiedSyncNotificationHandler) {
                    window.unifiedSyncNotificationHandler.registerCallback('contactSyncCompleted', function(data) {
                        console.log('🎉 [统一通知] 收到联系人同步完成通知:', data);
                        if (window.blazorContactComponent) {
                            window.blazorContactComponent.invokeMethodAsync('OnSyncCompleted');
                        }
                    });

                    window.unifiedSyncNotificationHandler.registerCallback('contactSyncProgress', function(data) {
                        console.log('📊 [统一通知] 收到联系人同步进度:', data);
                        if (window.blazorContactComponent) {
                            window.blazorContactComponent.invokeMethodAsync('OnProgressUpdate', data);
                        }
                    });

                    console.log('✅ 统一通知处理器回调注册成功');
                } else {
                    console.warn('⚠️ 统一通知处理器未初始化');
                }
            ");

            Logger.LogDebug("✅ 统一通知处理器回调注册成功");
        }
        catch (JSDisconnectedException)
        {
            Logger.LogDebug("Circuit已断开，跳过回调注册");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ 统一通知处理器回调注册失败");
        }
    }

    /// <summary>
    /// 等待JavaScript函数就绪
    /// </summary>
    private async Task<bool> WaitForJavaScriptFunctionAsync(string functionName, TimeSpan timeout)
    {
        var startTime = DateTime.UtcNow;
        var checkInterval = TimeSpan.FromMilliseconds(200);

        while (DateTime.UtcNow - startTime < timeout)
        {
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(1));
                var result = await JSRuntime.InvokeAsync<bool>("eval", cts.Token, $"typeof window.{functionName} === 'function'");
                if (result)
                {
                    Logger.LogDebug("JavaScript函数已就绪: {FunctionName}", functionName);
                    return true;
                }
            }
            catch (JSDisconnectedException)
            {
                Logger.LogDebug("Circuit已断开，停止等待JavaScript函数");
                return false;
            }
            catch (TaskCanceledException)
            {
                // 继续等待
            }
            catch (Exception ex)
            {
                Logger.LogDebug(ex, "检查JavaScript函数时出错: {FunctionName}", functionName);
            }

            await Task.Delay(checkInterval);
        }

        Logger.LogWarning("等待JavaScript函数超时: {FunctionName}, 超时时间: {Timeout}", functionName, timeout);
        return false;
    }

    /// <summary>
    /// 检查Circuit是否健康
    /// </summary>
    private async Task<bool> IsCircuitHealthyAsync()
    {
        try
        {
            // 使用简单的JavaScript调用来检测Circuit状态
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(2));
            await JSRuntime.InvokeVoidAsync("eval", cts.Token, "void 0");
            return true;
        }
        catch (JSDisconnectedException)
        {
            return false;
        }
    }

    /// <summary>
    /// 发送配置变更通知
    /// </summary>
    private async Task SendConfigurationChangeNotificationAsync(string configType, object changeData)
    {
        try
        {
            if (HasSelectedAccount)
            {
                // 使用JavaScript发送SignalR通知
                await JSRuntime.InvokeVoidAsync("sendConfigurationChangeNotification",
                    SelectedAccount.Id.ToString(), configType, changeData);

                Logger.LogDebug("✅ 配置变更通知已发送 - ConfigType: {ConfigType}, WxManagerId: {WxManagerId}",
                    configType, SelectedAccount.Id);
            }
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "⚠️ 发送配置变更通知失败 - ConfigType: {ConfigType}", configType);
        }
    }

}